//+------------------------------------------------------------------+
//| CRT_EA.mq5                                                       |
//| Candle Range Theory Expert Advisor                               |
//| Converted from TradingView Pine Script Indicator                 |
//+------------------------------------------------------------------+
#property copyright "CRT Strategy - Converted from TradingView"
#property link      "https://github.com/your-repo/crt-ea"
#property version   "0.10"
#property description "Candle Range Theory EA - Identical logic to TradingView indicator"
#property strict

//+------------------------------------------------------------------+
//| Includes                                                         |
//+------------------------------------------------------------------+
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//+------------------------------------------------------------------+
//| Core Data Structures                                            |
//+------------------------------------------------------------------+

// FVG Information Structure (equivalent to FVGInfo type)
struct SFVGInfo
{
   double            max;                    // FVG top level
   double            min;                    // FVG bottom level
   bool              isBull;                 // Bullish FVG flag
   datetime          startTime;              // FVG start time
   double            totalVolume;            // Total volume
   int               startBarIndex;          // Start bar index
   int               endBarIndex;            // End bar index
   datetime          endTime;                // End time
   bool              extendInfinite;         // Extend infinite flag
   bool              combined;               // Combined flag
   string            combinedTimeframesStr;  // Combined timeframes
   bool              disabled;               // Disabled flag
   string            timeframeStr;           // Timeframe string
   double            lowVolume;              // Low volume
   double            highVolume;             // High volume
   bool              isInverse;              // Inverse FVG flag
   datetime          lastTouched;            // Last touched time
   datetime          lastTouchedIFVG;        // Last touched IFVG time
   int               inverseEndIndex;        // Inverse end index
   datetime          inverseEndTime;         // Inverse end time
   double            inverseVolume;          // Inverse volume
};

// Order Block Information Structure (equivalent to orderBlockInfo type)
struct SOrderBlockInfo
{
   double            top;                    // Order block top
   double            bottom;                 // Order block bottom
   double            obVolume;               // Order block volume
   string            obType;                 // Order block type
   datetime          startTime;              // Start time
   double            bbVolume;               // Breaker block volume
   double            obLowVolume;            // OB low volume
   double            obHighVolume;           // OB high volume
   bool              breaker;                // Breaker flag
   datetime          breakTime;              // Break time
   datetime          breakerEndTime;         // Breaker end time
   string            timeframeStr;           // Timeframe string
   bool              disabled;               // Disabled flag
   string            combinedTimeframesStr;  // Combined timeframes
   bool              combined;               // Combined flag
};

// Main CRT Setup Structure (equivalent to CRT type)
struct SCRTSetup
{
   ENUM_CRT_STATE    state;                  // Current state
   datetime          startTime;              // Setup start time

   ENUM_OVERLAP_DIRECTION overlapDirection;  // Overlap direction
   datetime          bulkyTimeLow;           // Bulky candle low time
   datetime          bulkyTimeHigh;          // Bulky candle high time
   double            bulkyHigh;              // Bulky candle high
   double            bulkyLow;               // Bulky candle low
   datetime          breakTime;              // Break time

   SFVGInfo          fvg;                    // FVG information
   datetime          fvgEndTime;             // FVG end time
   SOrderBlockInfo   ob;                     // Order block information

   double            slTarget;               // Stop loss target
   double            tpTarget;               // Take profit target
   string            entryType;              // Entry type (Long/Short)
   datetime          entryTime;              // Entry time
   datetime          exitTime;               // Exit time
   double            entryPrice;             // Entry price
   double            exitPrice;              // Exit price
   int               dayEndedBeforeExit;     // Day ended before exit

   // Enhanced R:R fields
   double            qualityScore;           // Setup quality score
   double            dynamicRR;              // Dynamic R:R ratio

   // Intelligent Partial Profit Taking fields
   double            partialTP1;             // First partial TP level
   double            partialTP2;             // Second partial TP level
   bool              tp1Hit;                 // TP1 hit flag
   bool              tp2Hit;                 // TP2 hit flag
   double            remainingPosition;      // Remaining position percentage
   double            trailingStop;           // Trailing stop level
   bool              trailingActive;         // Trailing stop active flag
   datetime          tp1HitTime;             // TP1 hit time
   datetime          tp2HitTime;             // TP2 hit time

   // Win/Loss Visual System fields
   string            tradeResult;            // Trade result (Win/Loss/Breakeven)
   double            actualRR;               // Actual R:R achieved
   bool              showResult;             // Show result flag

   // Trade management fields
   ulong             positionTicket;         // Position ticket
   ulong             orderTickets[10];       // Order tickets array
   int               orderCount;             // Number of orders
};

// Bar Information Structure (equivalent to barInfo type)
struct SBarInfo
{
   double            open;                   // Open price
   double            high;                   // High price
   double            low;                    // Low price
   double            close;                  // Close price
   double            trueRange;              // True range
   double            atr;                    // ATR value
};

//+------------------------------------------------------------------+
//| Enumerations                                                     |
//+------------------------------------------------------------------+
enum ENUM_CRT_STATE
{
   CRT_WAITING_FOR_BULKY_CANDLE,    // Waiting For Bulky Candle
   CRT_WAITING_FOR_SIDE_RETEST,     // Waiting For Side Retest
   CRT_WAITING_FOR_FVG_OB,          // Waiting For FVG/OB
   CRT_ENTER_POSITION,              // Enter Position
   CRT_ENTRY_TAKEN,                 // Entry Taken
   CRT_ABORTED                      // Aborted
};

enum ENUM_OVERLAP_DIRECTION
{
   OVERLAP_BULL,                    // Bull Overlap
   OVERLAP_BEAR,                    // Bear Overlap
   OVERLAP_NONE                     // No Overlap
};

enum ENUM_ENTRY_MODE
{
   ENTRY_FVGS,                      // FVGs
   ENTRY_ORDER_BLOCKS               // Order Blocks
};

enum ENUM_TPSL_METHOD
{
   TPSL_DYNAMIC,                    // Dynamic
   TPSL_FIXED                       // Fixed
};

enum ENUM_RISK_AMOUNT
{
   RISK_HIGHEST,                    // Highest
   RISK_HIGH,                       // High
   RISK_NORMAL,                     // Normal
   RISK_LOW,                        // Low
   RISK_LOWEST                      // Lowest
};

enum ENUM_CANDLE_SIZE
{
   CANDLE_BIG,                      // Big
   CANDLE_NORMAL,                   // Normal
   CANDLE_SMALL                     // Small
};

//+------------------------------------------------------------------+
//| Input Parameters - General Configuration                         |
//+------------------------------------------------------------------+
input group "General Configuration"
input ENUM_TIMEFRAMES    InpHigherTF = PERIOD_H4;                    // Higher Timeframe
input ENUM_CANDLE_SIZE   InpBulkyCandleSize = CANDLE_BIG;           // HTF Candle Size
input ENUM_ENTRY_MODE    InpEntryMode = ENTRY_FVGS;                 // Entry Mode
input bool               InpRequireRetracement = false;              // Require Retracement
input bool               InpShowHTFLines = true;                     // Show HTF Candle Lines

//+------------------------------------------------------------------+
//| Input Parameters - TP/SL Configuration                          |
//+------------------------------------------------------------------+
input group "TP / SL"
input bool               InpShowTPSL = true;                         // Enabled
input ENUM_TPSL_METHOD   InpTPSLMethod = TPSL_DYNAMIC;              // TP / SL Method
input ENUM_RISK_AMOUNT   InpRiskAmount = RISK_HIGH;                 // Dynamic Risk
input double             InpTPPercent = 0.3;                        // Fixed Take Profit %
input double             InpSLPercent = 0.4;                        // Fixed Stop Loss %

//+------------------------------------------------------------------+
//| Input Parameters - Enhanced R:R System                          |
//+------------------------------------------------------------------+
input group "Enhanced R:R"
input bool               InpUseEnhancedRR = false;                   // Enable Enhanced R:R System
input double             InpMinRR = 1.5;                            // Minimum R:R Ratio (1.0-6.0)
input double             InpMaxRR = 2.5;                            // Maximum R:R Ratio (1.5-10.0)
input double             InpMinQualityScore = 2.0;                   // Minimum Quality Score (0.0-5.0)
input bool               InpRequireVolumeConfirmation = false;       // Require Volume Confirmation

//+------------------------------------------------------------------+
//| Input Parameters - Partial Profit Taking                        |
//+------------------------------------------------------------------+
input group "Partial Profit Taking"
input bool               InpUsePartialTPs = false;                   // Enable Partial TPs
input double             InpPartialTP1Percent = 30.0;               // First TP Percentage (10.0-50.0)
input double             InpPartialTP2Percent = 40.0;               // Second TP Percentage (10.0-60.0)
input bool               InpUseTrailingStop = true;                  // Enable Trailing Stop
input double             InpTrailingATRMult = 2.0;                  // Trailing Stop ATR Multiplier (0.5-5.0)

//+------------------------------------------------------------------+
//| Input Parameters - Time-Based Filtering                         |
//+------------------------------------------------------------------+
input group "Time Filtering"
input bool               InpUseTimeFiltering = false;                // Enable Time Filtering
input bool               InpAvoidWeekends = false;                   // Avoid Weekends
input bool               InpAvoidLunchHours = false;                 // Avoid Lunch Hours (12:00-13:00)
input bool               InpAvoidEarlyMorning = false;               // Avoid Early Morning (00:00-06:00)
input bool               InpUseCustomTimeRange = false;              // Use Custom Time Range
input int                InpCustomTimeStart = 9;                     // Trading Start Hour (0-23)
input int                InpCustomTimeEnd = 17;                      // Trading End Hour (0-23)
input bool               InpTradeLondonOnly = false;                 // Trade London Session Only (08:00-16:00)
input bool               InpTradeNewYorkOnly = false;                // Trade New York Session Only (13:00-21:00)
input bool               InpAvoidAsianSession = false;               // Avoid Asian Session (21:00-06:00)
input bool               InpAvoidMondays = false;                    // Avoid Mondays
input bool               InpAvoidFridays = false;                    // Avoid Fridays
input bool               InpAvoidFirstHour = false;                  // Avoid First Hour (09:00-10:00)
input bool               InpAvoidLastHour = false;                   // Avoid Last Hour (16:00-17:00)

//+------------------------------------------------------------------+
//| Input Parameters - Visual Enhancements                          |
//+------------------------------------------------------------------+
input group "Visual Enhancements"
input bool               InpShowWinLossMarkers = true;               // Show Win/Loss Markers
input bool               InpShowPositionProjection = true;           // Show Position Projection

//+------------------------------------------------------------------+
//| Input Parameters - Risk Management                              |
//+------------------------------------------------------------------+
input group "Risk Management"
input double             InpLotSize = 0.01;                         // Lot Size
input double             InpMaxRiskPercent = 2.0;                    // Maximum Risk Per Trade %
input int                InpMaxConcurrentTrades = 1;                // Maximum Concurrent Trades
input bool               InpUseFixedLots = true;                     // Use Fixed Lot Size

//+------------------------------------------------------------------+
//| Input Parameters - Alerts                                       |
//+------------------------------------------------------------------+
input group "Alerts"
input bool               InpBuyAlertEnabled = true;                  // Buy Signal
input bool               InpSellAlertEnabled = true;                 // Sell Signal
input bool               InpTPAlertEnabled = true;                   // Take-Profit Signal
input bool               InpSLAlertEnabled = true;                   // Stop-Loss Signal

//+------------------------------------------------------------------+
//| HTF Data Handler Class                                          |
//+------------------------------------------------------------------+
class CHTFDataHandler
{
private:
   ENUM_TIMEFRAMES m_htfPeriod;              // Higher timeframe period
   int             m_htfATRHandle;            // HTF ATR handle
   double          m_htfATRBuffer[];          // HTF ATR buffer
   MqlRates        m_htfRates[];              // HTF rates buffer
   SBarInfo        m_currentBar;              // Current HTF bar
   SBarInfo        m_previousBar;             // Previous HTF bar
   datetime        m_lastBarTime;             // Last processed bar time
   bool            m_initialized;             // Initialization flag

public:
   // Constructor
   CHTFDataHandler(ENUM_TIMEFRAMES htfPeriod)
   {
      m_htfPeriod = htfPeriod;
      m_htfATRHandle = INVALID_HANDLE;
      m_lastBarTime = 0;
      m_initialized = false;
      ArraySetAsSeries(m_htfATRBuffer, true);
      ArraySetAsSeries(m_htfRates, true);
   }

   // Destructor
   ~CHTFDataHandler()
   {
      if(m_htfATRHandle != INVALID_HANDLE)
      {
         IndicatorRelease(m_htfATRHandle);
         m_htfATRHandle = INVALID_HANDLE;
      }
   }

   // Initialize HTF data handler
   bool Initialize()
   {
      // Create HTF ATR indicator
      m_htfATRHandle = iATR(_Symbol, m_htfPeriod, 50);
      if(m_htfATRHandle == INVALID_HANDLE)
      {
         LogError("Failed to create HTF ATR indicator handle for timeframe: " + EnumToString(m_htfPeriod));
         return false;
      }

      // Wait for indicator to calculate
      if(!WaitForIndicator(m_htfATRHandle))
      {
         LogError("HTF ATR indicator failed to initialize");
         return false;
      }

      // Initialize with current data
      if(!UpdateHTFData())
      {
         LogError("Failed to initialize HTF data");
         return false;
      }

      m_initialized = true;
      LogInfo("HTF Data Handler initialized for timeframe: " + EnumToString(m_htfPeriod));
      return true;
   }

   // Update HTF data
   bool UpdateHTFData()
   {
      if(!m_initialized)
         return false;

      // Copy HTF rates
      if(CopyRates(_Symbol, m_htfPeriod, 0, 3, m_htfRates) < 3)
      {
         LogError("Failed to copy HTF rates");
         return false;
      }

      // Copy HTF ATR values
      if(CopyBuffer(m_htfATRHandle, 0, 0, 3, m_htfATRBuffer) < 3)
      {
         LogError("Failed to copy HTF ATR buffer");
         return false;
      }

      // Store previous bar data
      m_previousBar = m_currentBar;

      // Update current bar data
      m_currentBar.open = m_htfRates[0].open;
      m_currentBar.high = m_htfRates[0].high;
      m_currentBar.low = m_htfRates[0].low;
      m_currentBar.close = m_htfRates[0].close;
      m_currentBar.atr = m_htfATRBuffer[0];

      // Calculate True Range
      m_currentBar.trueRange = CalculateTrueRange(0);

      return true;
   }

   // Check if new HTF bar formed
   bool IsNewHTFBar()
   {
      if(!m_initialized)
         return false;

      datetime currentBarTime = m_htfRates[0].time;
      if(currentBarTime != m_lastBarTime)
      {
         m_lastBarTime = currentBarTime;
         return true;
      }
      return false;
   }

   // Get current HTF bar
   SBarInfo GetCurrentBar() const { return m_currentBar; }

   // Get previous HTF bar
   SBarInfo GetPreviousBar() const { return m_previousBar; }

   // Check if HTF data is valid
   bool IsDataValid() const
   {
      return m_initialized &&
             m_currentBar.high > 0 &&
             m_currentBar.low > 0 &&
             m_currentBar.atr > 0;
   }

private:
   // Calculate True Range for HTF bar
   double CalculateTrueRange(int index)
   {
      if(ArraySize(m_htfRates) <= index + 1)
         return 0.0;

      double high = m_htfRates[index].high;
      double low = m_htfRates[index].low;
      double prevClose = m_htfRates[index + 1].close;

      double tr1 = high - low;
      double tr2 = MathAbs(high - prevClose);
      double tr3 = MathAbs(low - prevClose);

      return MathMax(tr1, MathMax(tr2, tr3));
   }

   // Wait for indicator to be ready
   bool WaitForIndicator(int handle)
   {
      int attempts = 0;
      while(BarsCalculated(handle) < 2 && attempts < 100)
      {
         Sleep(50);
         attempts++;
      }
      return BarsCalculated(handle) >= 2;
   }
};

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade         g_trade;                    // Trade execution object
CPositionInfo  g_position;                 // Position information object
COrderInfo     g_order;                    // Order information object

// HTF Data Handler
CHTFDataHandler* g_htfHandler = NULL;       // HTF data handler instance

// EA State Variables
bool           g_isInitialized = false;    // EA initialization status
datetime       g_lastBarTime = 0;          // Last processed bar time
int            g_magicNumber = 123456;      // Magic number for EA trades
string         g_eaComment = "CRT_EA";      // EA comment for trades

// Configuration Variables
double         g_bulkyCandleATR = 2.1;     // Bulky candle ATR multiplier
double         g_slATRMult = 8.0;          // Stop loss ATR multiplier
double         g_finalTPPercent = 30.0;    // Final TP percentage (calculated)
bool           g_validPartialTPs = true;   // Partial TP validation status

// Market Data Variables
int            g_atrHandle = INVALID_HANDLE;  // ATR indicator handle
double         g_atrBuffer[];                 // ATR values buffer

// HTF Data Variables
int            g_htfATRHandle = INVALID_HANDLE;  // HTF ATR indicator handle
double         g_htfATRBuffer[];                 // HTF ATR values buffer
MqlRates       g_htfRates[];                     // HTF rates buffer
SBarInfo       g_currentHTFBar;                  // Current HTF bar
SBarInfo       g_previousHTFBar;                 // Previous HTF bar
bool           g_htfDataInitialized = false;     // HTF data initialization flag

// Bulky Candle Detection Variables
bool           g_newBulkyCandle = false;         // New bulky candle detected flag
double         g_lastHigh = 0.0;                 // Last bulky candle high
double         g_lastLow = 0.0;                  // Last bulky candle low
datetime       g_lastHTFBarTime = 0;             // Last HTF bar time processed

// Logging Variables
bool           g_debugMode = false;        // Debug mode flag
string         g_logPrefix = "[CRT_EA] ";  // Log message prefix

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print(g_logPrefix + "Initializing CRT Expert Advisor v0.10");
   
   // Validate input parameters
   if(!ValidateInputParameters())
   {
      Print(g_logPrefix + "ERROR: Invalid input parameters");
      return INIT_PARAMETERS_INCORRECT;
   }

   // Validate HTF timeframe
   if(!ValidateHTFTimeframe())
   {
      Print(g_logPrefix + "ERROR: Invalid higher timeframe setting");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // Initialize configuration
   if(!InitializeConfiguration())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize configuration");
      return INIT_FAILED;
   }
   
   // Initialize market data
   if(!InitializeMarketData())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize market data");
      return INIT_FAILED;
   }
   
   // Initialize trade management
   if(!InitializeTradeManagement())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize trade management");
      return INIT_FAILED;
   }
   
   g_isInitialized = true;
   Print(g_logPrefix + "Initialization completed successfully");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print(g_logPrefix + "Deinitializing CRT Expert Advisor");

   // Clean up indicators
   if(g_atrHandle != INVALID_HANDLE)
   {
      IndicatorRelease(g_atrHandle);
      g_atrHandle = INVALID_HANDLE;
   }

   // Clean up HTF data handler
   if(g_htfHandler != NULL)
   {
      delete g_htfHandler;
      g_htfHandler = NULL;
   }

   // Save state if needed
   SaveEAState();

   Print(g_logPrefix + "Deinitialization completed. Reason: " + IntegerToString(reason));
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if EA is properly initialized
   if(!g_isInitialized)
      return;
   
   // Check for new bar
   if(!IsNewBar())
      return;
   
   // Update market data
   if(!UpdateMarketData())
   {
      Print(g_logPrefix + "ERROR: Failed to update market data");
      return;
   }
   
   // Main strategy logic (placeholder for future phases)
   ProcessCRTStrategy();
   
   // Update trade management
   ManageActiveTrades();
}

//+------------------------------------------------------------------+
//| Validate input parameters                                       |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
   bool isValid = true;
   
   // Validate R:R parameters
   if(InpMinRR < 1.0 || InpMinRR > 6.0)
   {
      Print(g_logPrefix + "ERROR: Minimum R:R must be between 1.0 and 6.0");
      isValid = false;
   }
   
   if(InpMaxRR < 1.5 || InpMaxRR > 10.0)
   {
      Print(g_logPrefix + "ERROR: Maximum R:R must be between 1.5 and 10.0");
      isValid = false;
   }
   
   if(InpMinRR >= InpMaxRR)
   {
      Print(g_logPrefix + "ERROR: Minimum R:R must be less than Maximum R:R");
      isValid = false;
   }
   
   // Validate partial TP percentages
   if(InpPartialTP1Percent + InpPartialTP2Percent > 90.0)
   {
      Print(g_logPrefix + "ERROR: Partial TP percentages cannot exceed 90%");
      g_validPartialTPs = false;
   }
   else
   {
      g_validPartialTPs = true;
      g_finalTPPercent = 100.0 - InpPartialTP1Percent - InpPartialTP2Percent;
   }
   
   // Validate time parameters
   if(InpCustomTimeStart < 0 || InpCustomTimeStart > 23 || 
      InpCustomTimeEnd < 0 || InpCustomTimeEnd > 23)
   {
      Print(g_logPrefix + "ERROR: Custom time hours must be between 0 and 23");
      isValid = false;
   }
   
   // Validate risk parameters
   if(InpMaxRiskPercent <= 0 || InpMaxRiskPercent > 10.0)
   {
      Print(g_logPrefix + "ERROR: Maximum risk percent must be between 0 and 10%");
      isValid = false;
   }
   
   if(InpLotSize <= 0)
   {
      Print(g_logPrefix + "ERROR: Lot size must be greater than 0");
      isValid = false;
   }
   
   return isValid;
}

//+------------------------------------------------------------------+
//| Initialize configuration                                         |
//+------------------------------------------------------------------+
bool InitializeConfiguration()
{
   // Set bulky candle ATR multiplier based on size setting
   switch(InpBulkyCandleSize)
   {
      case CANDLE_BIG:    g_bulkyCandleATR = 2.1; break;
      case CANDLE_NORMAL: g_bulkyCandleATR = 1.6; break;
      case CANDLE_SMALL:  g_bulkyCandleATR = 1.3; break;
      default:            g_bulkyCandleATR = 2.1; break;
   }
   
   // Set stop loss ATR multiplier based on risk amount
   switch(InpRiskAmount)
   {
      case RISK_HIGHEST: g_slATRMult = 10.0; break;
      case RISK_HIGH:    g_slATRMult = 8.0;  break;
      case RISK_NORMAL:  g_slATRMult = 6.5;  break;
      case RISK_LOW:     g_slATRMult = 5.0;  break;
      case RISK_LOWEST:  g_slATRMult = 3.0;  break;
      default:           g_slATRMult = 8.0;  break;
   }
   
   // Set magic number and comment
   g_trade.SetExpertMagicNumber(g_magicNumber);
   
   Print(g_logPrefix + "Configuration initialized:");
   Print(g_logPrefix + "- Bulky Candle ATR: " + DoubleToString(g_bulkyCandleATR, 2));
   Print(g_logPrefix + "- SL ATR Multiplier: " + DoubleToString(g_slATRMult, 2));
   Print(g_logPrefix + "- Valid Partial TPs: " + (g_validPartialTPs ? "Yes" : "No"));
   Print(g_logPrefix + "- Final TP Percent: " + DoubleToString(g_finalTPPercent, 1) + "%");
   
   return true;
}

//+------------------------------------------------------------------+
//| Initialize market data                                           |
//+------------------------------------------------------------------+
bool InitializeMarketData()
{
   // Initialize ATR indicator
   g_atrHandle = iATR(_Symbol, PERIOD_CURRENT, 50);
   if(g_atrHandle == INVALID_HANDLE)
   {
      Print(g_logPrefix + "ERROR: Failed to create ATR indicator handle");
      return false;
   }

   // Initialize ATR buffer
   ArraySetAsSeries(g_atrBuffer, true);

   // Initialize HTF data handler
   g_htfHandler = new CHTFDataHandler(InpHigherTF);
   if(g_htfHandler == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create HTF data handler");
      return false;
   }

   if(!g_htfHandler.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize HTF data handler");
      delete g_htfHandler;
      g_htfHandler = NULL;
      return false;
   }

   Print(g_logPrefix + "Market data initialized successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Initialize trade management                                      |
//+------------------------------------------------------------------+
bool InitializeTradeManagement()
{
   // Set trade parameters
   g_trade.SetDeviationInPoints(10);
   g_trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   Print(g_logPrefix + "Trade management initialized successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Check for new bar                                               |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(currentBarTime != g_lastBarTime)
   {
      g_lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Update market data                                              |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
   // Update ATR values
   if(CopyBuffer(g_atrHandle, 0, 0, 3, g_atrBuffer) < 3)
   {
      Print(g_logPrefix + "ERROR: Failed to copy ATR buffer");
      return false;
   }

   // Update HTF data
   if(g_htfHandler != NULL)
   {
      if(!g_htfHandler.UpdateHTFData())
      {
         LogError("Failed to update HTF data");
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Detect new bulky candle (equivalent to Pine Script logic)      |
//+------------------------------------------------------------------+
bool DetectBulkyCandle()
{
   if(g_htfHandler == NULL || !g_htfHandler.IsDataValid())
      return false;

   // Reset bulky candle flag
   g_newBulkyCandle = false;

   // Check if new HTF bar formed
   if(!g_htfHandler.IsNewHTFBar())
      return false;

   // Get current and previous HTF bars
   SBarInfo currentBar = g_htfHandler.GetCurrentBar();
   SBarInfo previousBar = g_htfHandler.GetPreviousBar();

   // Check if we have valid previous bar data
   if(previousBar.high <= 0 || previousBar.low <= 0)
      return false;

   // Pine Script logic: oldHigherTFBar.h != higherTFBar.h and (higherTFBar.tr > higherTFBar.atr * bulkyCandleATR)
   bool newHTFBar = (previousBar.high != currentBar.high);
   bool sizeCriteria = (currentBar.trueRange > currentBar.atr * g_bulkyCandleATR);

   if(newHTFBar && sizeCriteria)
   {
      g_newBulkyCandle = true;
      g_lastHigh = currentBar.high;
      g_lastLow = currentBar.low;

      LogInfo("New Bulky Candle Detected - High: " + DoubleToString(g_lastHigh, _Digits) +
              ", Low: " + DoubleToString(g_lastLow, _Digits) +
              ", TR: " + DoubleToString(currentBar.trueRange, _Digits) +
              ", ATR: " + DoubleToString(currentBar.atr, _Digits) +
              ", Multiplier: " + DoubleToString(g_bulkyCandleATR, 2));

      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Get bulky candle detection status                               |
//+------------------------------------------------------------------+
bool IsNewBulkyCandle()
{
   return g_newBulkyCandle;
}

//+------------------------------------------------------------------+
//| Get last bulky candle high                                      |
//+------------------------------------------------------------------+
double GetLastBulkyHigh()
{
   return g_lastHigh;
}

//+------------------------------------------------------------------+
//| Get last bulky candle low                                       |
//+------------------------------------------------------------------+
double GetLastBulkyLow()
{
   return g_lastLow;
}

//+------------------------------------------------------------------+
//| Validate HTF timeframe setting                                  |
//+------------------------------------------------------------------+
bool ValidateHTFTimeframe()
{
   // Check if higher timeframe is actually higher than current
   int currentTFSeconds = PeriodSeconds(PERIOD_CURRENT);
   int htfSeconds = PeriodSeconds(InpHigherTF);

   if(htfSeconds <= currentTFSeconds)
   {
      LogError("Higher timeframe must be higher than current timeframe. Current: " +
               EnumToString(PERIOD_CURRENT) + ", HTF: " + EnumToString(InpHigherTF));
      return false;
   }

   LogInfo("HTF validation passed. Current: " + EnumToString(PERIOD_CURRENT) +
           ", HTF: " + EnumToString(InpHigherTF));
   return true;
}

//+------------------------------------------------------------------+
//| Main CRT strategy processing                                    |
//+------------------------------------------------------------------+
void ProcessCRTStrategy()
{
   // Phase 2: Bulky candle detection
   DetectBulkyCandle();

   // Log bulky candle detection for debugging
   if(g_debugMode && IsNewBulkyCandle())
   {
      LogDebug("New bulky candle detected - High: " + DoubleToString(GetLastBulkyHigh(), _Digits) +
               ", Low: " + DoubleToString(GetLastBulkyLow(), _Digits));
   }

   // Placeholder for subsequent phases
   // Phase 3: FVG & Order Block Detection
   // Phase 4: Core Strategy Logic (State Machine)
   // Phase 5: Entry Validation & Filtering
   // Phase 6-8: Risk Management & Trade Execution
}

//+------------------------------------------------------------------+
//| Manage active trades (placeholder)                              |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
   // Placeholder for trade management logic
   // This will be implemented in subsequent phases
   
   if(g_debugMode)
   {
      Print(g_logPrefix + "Managing active trades - Phase 1 placeholder");
   }
}

//+------------------------------------------------------------------+
//| Save EA state                                                   |
//+------------------------------------------------------------------+
void SaveEAState()
{
   // Placeholder for state persistence
   // This will be implemented in Phase 9
   
   if(g_debugMode)
   {
      Print(g_logPrefix + "Saving EA state - Phase 1 placeholder");
   }
}

//+------------------------------------------------------------------+
//| Utility function: Get current ATR value                        |
//+------------------------------------------------------------------+
double GetCurrentATR()
{
   if(ArraySize(g_atrBuffer) > 0)
      return g_atrBuffer[0];
   return 0.0;
}

//+------------------------------------------------------------------+
//| Utility function: Calculate lot size based on risk             |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskAmount, double stopLossDistance)
{
   if(InpUseFixedLots)
      return InpLotSize;

   // Risk-based position sizing (placeholder)
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskMoney = accountBalance * (InpMaxRiskPercent / 100.0);
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   if(stopLossDistance > 0 && tickValue > 0 && tickSize > 0)
   {
      double lotSize = riskMoney / (stopLossDistance * tickValue / tickSize);
      double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
      double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
      lotSize = MathFloor(lotSize / lotStep) * lotStep;

      return lotSize;
   }

   return InpLotSize;
}

//+------------------------------------------------------------------+
//| Utility function: Validate time-based filtering                |
//+------------------------------------------------------------------+
bool IsValidTradingTime()
{
   if(!InpUseTimeFiltering)
      return true;

   bool valid = true;
   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);
   int currentHour = timeStruct.hour;
   int currentDayOfWeek = timeStruct.day_of_week;

   // Weekend filter
   if(InpAvoidWeekends && (currentDayOfWeek == 0 || currentDayOfWeek == 6)) // Sunday = 0, Saturday = 6
      valid = false;

   // Lunch hours filter (12:00-13:00)
   if(InpAvoidLunchHours && (currentHour >= 12 && currentHour < 13))
      valid = false;

   // Early morning filter (00:00-06:00)
   if(InpAvoidEarlyMorning && (currentHour >= 0 && currentHour < 6))
      valid = false;

   // Major trading session filters
   if(InpTradeLondonOnly && (currentHour < 8 || currentHour >= 16))
      valid = false;

   if(InpTradeNewYorkOnly && (currentHour < 13 || currentHour >= 21))
      valid = false;

   if(InpAvoidAsianSession && (currentHour >= 21 || currentHour < 6))
      valid = false;

   // Day of week filters
   if(InpAvoidMondays && currentDayOfWeek == 1) // Monday = 1
      valid = false;

   if(InpAvoidFridays && currentDayOfWeek == 5) // Friday = 5
      valid = false;

   // Session timing filters
   if(InpAvoidFirstHour && (currentHour >= 9 && currentHour < 10))
      valid = false;

   if(InpAvoidLastHour && (currentHour >= 16 && currentHour < 17))
      valid = false;

   // Custom time range filter
   if(InpUseCustomTimeRange)
   {
      if(InpCustomTimeStart <= InpCustomTimeEnd)
      {
         // Normal range (e.g., 9-17)
         if(currentHour < InpCustomTimeStart || currentHour >= InpCustomTimeEnd)
            valid = false;
      }
      else
      {
         // Overnight range (e.g., 22-06)
         if(currentHour >= InpCustomTimeEnd && currentHour < InpCustomTimeStart)
            valid = false;
      }
   }

   return valid;
}

//+------------------------------------------------------------------+
//| Utility function: Validate volume confirmation                 |
//+------------------------------------------------------------------+
bool IsVolumeValid()
{
   if(!InpRequireVolumeConfirmation)
      return true;

   // Get current volume and average volume
   long currentVolume = iVolume(_Symbol, PERIOD_CURRENT, 0);

   // Calculate average volume over 20 periods
   long totalVolume = 0;
   for(int i = 1; i <= 20; i++)
   {
      totalVolume += iVolume(_Symbol, PERIOD_CURRENT, i);
   }
   double avgVolume = (double)totalVolume / 20.0;

   // Volume must be at least 10% above average
   return (currentVolume >= avgVolume * 1.1);
}

//+------------------------------------------------------------------+
//| Utility function: Calculate setup quality score               |
//+------------------------------------------------------------------+
double CalculateQualityScore(const SCRTSetup &setup)
{
   double score = 0.0;

   // Base score from bulky candle range (placeholder logic)
   double currentATR = GetCurrentATR();
   if(currentATR > 0)
   {
      double bulkyRange = setup.bulkyHigh - setup.bulkyLow;
      double rangeRatio = bulkyRange / currentATR;

      if(rangeRatio >= 2.5)      score += 2.0;  // Excellent range
      else if(rangeRatio >= 2.0) score += 1.5;  // Good range
      else if(rangeRatio >= 1.5) score += 1.0;  // Average range
      else                       score += 0.5;  // Poor range
   }

   // Volume confirmation bonus
   if(IsVolumeValid())
      score += 1.0;

   // Time-based bonus
   if(IsValidTradingTime())
      score += 0.5;

   // Additional quality factors (placeholder)
   // - FVG/OB strength
   // - Market structure alignment
   // - Confluence factors

   return MathMin(5.0, MathMax(0.0, score)); // Clamp between 0-5
}

//+------------------------------------------------------------------+
//| Utility function: Calculate dynamic R:R based on quality      |
//+------------------------------------------------------------------+
double CalculateDynamicRR(double qualityScore)
{
   if(!InpUseEnhancedRR)
      return 0.39; // Original static R:R

   // Linear interpolation between min and max R:R based on quality
   double normalizedScore = qualityScore / 5.0; // Normalize to 0-1
   double dynamicRR = InpMinRR + (normalizedScore * (InpMaxRR - InpMinRR));

   return MathMin(InpMaxRR, MathMax(InpMinRR, dynamicRR));
}

//+------------------------------------------------------------------+
//| Utility function: Calculate R:R ratio achieved                |
//+------------------------------------------------------------------+
double CalculateRRRatio(const SCRTSetup &setup)
{
   if(setup.entryPrice == 0 || setup.slTarget == 0 || setup.exitPrice == 0)
      return 0.0;

   // Calculate risk (distance to stop loss)
   double risk = MathAbs(setup.entryPrice - setup.slTarget);

   // Calculate actual reward/loss (distance to exit)
   double reward = 0.0;
   if(setup.entryType == "Long")
      reward = setup.exitPrice - setup.entryPrice;
   else if(setup.entryType == "Short")
      reward = setup.entryPrice - setup.exitPrice;

   // Return R:R ratio (positive for profit, negative for loss)
   return (risk > 0) ? reward / risk : 0.0;
}

//+------------------------------------------------------------------+
//| Utility function: Format time for logging                      |
//+------------------------------------------------------------------+
string FormatTime(datetime time)
{
   return TimeToString(time, TIME_DATE | TIME_MINUTES);
}

//+------------------------------------------------------------------+
//| Utility function: Log debug message                            |
//+------------------------------------------------------------------+
void LogDebug(string message)
{
   if(g_debugMode)
   {
      Print(g_logPrefix + "[DEBUG] " + message);
   }
}

//+------------------------------------------------------------------+
//| Utility function: Log info message                             |
//+------------------------------------------------------------------+
void LogInfo(string message)
{
   Print(g_logPrefix + "[INFO] " + message);
}

//+------------------------------------------------------------------+
//| Utility function: Log error message                            |
//+------------------------------------------------------------------+
void LogError(string message)
{
   Print(g_logPrefix + "[ERROR] " + message);
}

//+------------------------------------------------------------------+

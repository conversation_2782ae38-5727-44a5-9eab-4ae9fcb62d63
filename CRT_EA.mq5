//+------------------------------------------------------------------+
//| CRT_EA.mq5                                                       |
//| Candle Range Theory Expert Advisor                               |
//| Converted from TradingView Indicator with Identical Logic        |
//+------------------------------------------------------------------+
#property copyright "CRT Trading System"
#property link      ""
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>

//--- Magic number for this EA
#define MAGIC_NUMBER 20241218

//--- Enumerations
enum ENUM_ENTRY_MODE {
    ENTRY_MODE_FVGS = 0,    // Fair Value Gaps
    ENTRY_MODE_OB = 1       // Order Blocks
};

enum ENUM_TPSL_METHOD {
    TPSL_FIXED = 0,         // Fixed Percentage
    TPSL_DYNAMIC = 1        // Dynamic ATR-based
};

//+------------------------------------------------------------------+
//| Input Parameters - Exact replica of TradingView indicator       |
//+------------------------------------------------------------------+

//--- Core CRT Settings
input group "=== Core CRT Settings ==="
input int InpATRLength = 14;                                    // ATR Length
input double InpATRMultiplier = 2.0;                           // ATR Multiplier for Bulky Candles
input int InpMaxCRT = 5;                                        // Maximum CRT Instances
input ENUM_ENTRY_MODE InpEntryMode = ENTRY_MODE_FVGS;          // Entry Mode
input bool InpRequireRetracement = true;                       // Require Retracement

//--- TP/SL Settings
input group "=== TP/SL Settings ==="
input ENUM_TPSL_METHOD InpTPSLMethod = TPSL_DYNAMIC;           // TP/SL Method
input double InpTPPercent = 2.0;                               // Take Profit %
input double InpSLPercent = 1.0;                               // Stop Loss %
input double InpSLATRMult = 1.0;                               // SL ATR Multiplier
input double InpDynamicRR = 1.5;                               // Dynamic R:R Ratio

//--- Enhanced R:R System
input group "=== Enhanced R:R System ==="
input bool InpUseEnhancedRR = false;                           // Enable Enhanced R:R
input double InpMinRR = 1.5;                                   // Minimum R:R Ratio
input double InpMaxRR = 2.5;                                   // Maximum R:R Ratio
input double InpMinQualityScore = 2.0;                         // Minimum Quality Score
input bool InpRequireVolumeConfirmation = false;               // Require Volume Confirmation

//--- Partial Profit Taking
input group "=== Partial Profit Taking ==="
input bool InpUsePartialTPs = false;                           // Enable Partial TPs
input double InpPartialTP1Percent = 30.0;                      // Partial TP1 %
input double InpPartialTP2Percent = 40.0;                      // Partial TP2 %
input bool InpUseTrailingStop = true;                          // Use Trailing Stop
input double InpTrailingATRMult = 0.5;                         // Trailing Stop ATR Multiplier

//--- Time-Based Filtering
input group "=== Time Filtering ==="
input bool InpUseTimeFiltering = false;                        // Enable Time Filtering
input bool InpAvoidWeekends = false;                           // Avoid Weekends
input bool InpAvoidLunchHours = false;                         // Avoid Lunch Hours (12:00-13:00)
input bool InpAvoidEarlyMorning = false;                       // Avoid Early Morning (00:00-06:00)
input bool InpUseCustomTimeRange = false;                      // Use Custom Time Range
input int InpCustomTimeStart = 9;                              // Trading Start Hour
input int InpCustomTimeEnd = 17;                               // Trading End Hour
input bool InpTradeLondonOnly = false;                         // Trade London Session Only (08:00-16:00)
input bool InpTradeNewYorkOnly = false;                        // Trade New York Session Only (13:00-21:00)
input bool InpAvoidAsianSession = false;                       // Avoid Asian Session (21:00-06:00)
input bool InpAvoidMondays = false;                            // Avoid Mondays
input bool InpAvoidFridays = false;                            // Avoid Fridays
input bool InpAvoidFirstHour = false;                          // Avoid First Hour (09:00-10:00)
input bool InpAvoidLastHour = false;                           // Avoid Last Hour (16:00-17:00)

//--- Risk Management
input group "=== Risk Management ==="
input double InpLotSize = 0.1;                                 // Lot Size
input double InpRiskPercent = 2.0;                             // Risk Percentage
input bool InpUseFixedLots = true;                             // Use Fixed Lot Size
input int InpMaxSpread = 30;                                   // Maximum Spread (points)

//--- Alerts and Logging
input group "=== Alerts and Logging ==="
input bool InpEnableAlerts = true;                             // Enable Alerts
input bool InpEnableLogging = true;                            // Enable Detailed Logging
input bool InpLogTrades = true;                                // Log All Trades

//+------------------------------------------------------------------+
//| CRT Data Structure - Replica of TradingView CRT type            |
//+------------------------------------------------------------------+
struct SCRTData {
    string state;                    // Current state
    datetime entryTime;              // Entry time
    datetime exitTime;               // Exit time
    double entryPrice;               // Entry price
    double exitPrice;                // Exit price
    double bulkyHigh;                // Bulky candle high
    double bulkyLow;                 // Bulky candle low
    datetime bulkyTimeHigh;          // Bulky high time
    datetime bulkyTimeLow;           // Bulky low time
    datetime breakTime;              // Break time
    string overlapDirection;         // "Bull" or "Bear"
    string entryType;                // "Long" or "Short"
    double slTarget;                 // Stop loss target
    double tpTarget;                 // Take profit target
    
    // Enhanced R:R fields
    double qualityScore;             // Setup quality score
    double dynamicRR;                // Dynamic R:R ratio
    
    // Partial TP fields
    double partialTP1;               // Partial TP1 level
    double partialTP2;               // Partial TP2 level
    bool tp1Hit;                     // TP1 hit flag
    bool tp2Hit;                     // TP2 hit flag
    double remainingPosition;        // Remaining position %
    double trailingStop;             // Trailing stop level
    bool trailingActive;             // Trailing stop active
    datetime tp1HitTime;             // TP1 hit time
    datetime tp2HitTime;             // TP2 hit time
    
    // Win/Loss tracking
    string tradeResult;              // "Win", "Loss", "Breakeven"
    double actualRR;                 // Actual R:R achieved
    bool showResult;                 // Show result flag
    
    // MT5 specific fields
    ulong ticket;                    // Order ticket
    double originalLots;             // Original lot size
    double remainingLots;            // Remaining lots
};

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
SCRTData g_currentCRT;              // Current CRT instance
CTrade g_trade;                     // Trade object
bool g_initRun = true;              // Initialization flag
datetime g_lastBarTime = 0;         // Last bar time for new bar detection
int g_atrHandle = INVALID_HANDLE;   // ATR indicator handle

// Performance tracking
int g_totalTrades = 0;
int g_winningTrades = 0;
int g_losingTrades = 0;
double g_totalRR = 0.0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Initialize trade object
    g_trade.SetExpertMagicNumber(MAGIC_NUMBER);
    g_trade.SetDeviationInPoints(10);
    g_trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    // Initialize ATR indicator
    g_atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpATRLength);
    if(g_atrHandle == INVALID_HANDLE) {
        Print("Failed to create ATR indicator handle");
        return INIT_FAILED;
    }
    
    // Initialize CRT state
    InitializeCRT();
    
    // Validate inputs
    if(!ValidateInputs()) {
        return INIT_PARAMETERS_INCORRECT;
    }
    
    Print("CRT EA initialized successfully");
    if(InpEnableLogging) {
        Print("Enhanced R:R: ", InpUseEnhancedRR ? "Enabled" : "Disabled");
        Print("Partial TPs: ", InpUsePartialTPs ? "Enabled" : "Disabled");
        Print("Time Filtering: ", InpUseTimeFiltering ? "Enabled" : "Disabled");
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Release indicator handle
    if(g_atrHandle != INVALID_HANDLE) {
        IndicatorRelease(g_atrHandle);
    }
    
    // Print final statistics
    if(InpEnableLogging && g_totalTrades > 0) {
        Print("=== CRT EA Final Statistics ===");
        Print("Total Trades: ", g_totalTrades);
        Print("Winning Trades: ", g_winningTrades);
        Print("Losing Trades: ", g_losingTrades);
        Print("Win Rate: ", DoubleToString(g_winningTrades * 100.0 / g_totalTrades, 2), "%");
        Print("Total R:R: ", DoubleToString(g_totalRR, 2), "R");
        Print("Average R:R: ", DoubleToString(g_totalRR / g_totalTrades, 2), "R");
    }
    
    Print("CRT EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function - Main logic execution                     |
//+------------------------------------------------------------------+
void OnTick() {
    // Check for new bar
    if(!IsNewBar()) return;
    
    // Skip if spread is too high
    if(GetSpread() > InpMaxSpread) {
        if(InpEnableLogging) Print("Spread too high: ", GetSpread(), " points");
        return;
    }
    
    // Main CRT logic execution
    ProcessCRTLogic();
    
    // Manage existing positions
    ManageExistingPositions();
}

//+------------------------------------------------------------------+
//| Initialize CRT structure                                         |
//+------------------------------------------------------------------+
void InitializeCRT() {
    g_currentCRT.state = "Waiting For Bulky Candle";
    g_currentCRT.entryTime = 0;
    g_currentCRT.exitTime = 0;
    g_currentCRT.entryPrice = 0;
    g_currentCRT.exitPrice = 0;
    g_currentCRT.bulkyHigh = 0;
    g_currentCRT.bulkyLow = 0;
    g_currentCRT.bulkyTimeHigh = 0;
    g_currentCRT.bulkyTimeLow = 0;
    g_currentCRT.breakTime = 0;
    g_currentCRT.overlapDirection = "";
    g_currentCRT.entryType = "";
    g_currentCRT.slTarget = 0;
    g_currentCRT.tpTarget = 0;
    g_currentCRT.qualityScore = 0;
    g_currentCRT.dynamicRR = InpDynamicRR;
    g_currentCRT.partialTP1 = 0;
    g_currentCRT.partialTP2 = 0;
    g_currentCRT.tp1Hit = false;
    g_currentCRT.tp2Hit = false;
    g_currentCRT.remainingPosition = 100.0;
    g_currentCRT.trailingStop = 0;
    g_currentCRT.trailingActive = false;
    g_currentCRT.tp1HitTime = 0;
    g_currentCRT.tp2HitTime = 0;
    g_currentCRT.tradeResult = "";
    g_currentCRT.actualRR = 0;
    g_currentCRT.showResult = false;
    g_currentCRT.ticket = 0;
    g_currentCRT.originalLots = 0;
    g_currentCRT.remainingLots = 0;
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputs() {
    if(InpLotSize <= 0) {
        Print("Error: Lot size must be positive");
        return false;
    }

    if(InpRiskPercent <= 0 || InpRiskPercent > 100) {
        Print("Error: Risk percentage must be between 0 and 100");
        return false;
    }

    if(InpUsePartialTPs) {
        double finalTPPercent = 100.0 - InpPartialTP1Percent - InpPartialTP2Percent;
        if(finalTPPercent <= 0) {
            Print("Error: Partial TP percentages sum exceeds 100%");
            return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if new bar has formed                                      |
//+------------------------------------------------------------------+
bool IsNewBar() {
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(currentBarTime != g_lastBarTime) {
        g_lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get current spread in points                                     |
//+------------------------------------------------------------------+
int GetSpread() {
    return (int)((Ask - Bid) / _Point);
}

//+------------------------------------------------------------------+
//| Calculate ATR value                                              |
//+------------------------------------------------------------------+
double CalculateATR(int shift = 1) {
    double atrBuffer[];
    if(CopyBuffer(g_atrHandle, 0, shift, 1, atrBuffer) <= 0) {
        Print("Error copying ATR buffer: ", GetLastError());
        return 0;
    }
    return atrBuffer[0];
}

//+------------------------------------------------------------------+
//| Detect bulky candle                                              |
//+------------------------------------------------------------------+
bool IsBulkyCandle(int shift = 1) {
    double high = iHigh(_Symbol, PERIOD_CURRENT, shift);
    double low = iLow(_Symbol, PERIOD_CURRENT, shift);
    double range = high - low;
    double atr = CalculateATR(shift);

    if(atr <= 0) return false;

    return range >= atr * InpATRMultiplier;
}

//+------------------------------------------------------------------+
//| Calculate setup quality score                                    |
//+------------------------------------------------------------------+
double CalculateQualityScore(double bulkyRange, long currentVolume) {
    double score = 0.0;
    double atr = CalculateATR(1);

    // Calculate average volume
    long avgVolume = 0;
    for(int i = 1; i <= 20; i++) {
        avgVolume += iVolume(_Symbol, PERIOD_CURRENT, i);
    }
    avgVolume /= 20;

    // Bulky candle size factor
    if(bulkyRange > atr * 3.0) score += 3.0;
    else if(bulkyRange > atr * 2.5) score += 2.0;
    else if(bulkyRange > atr * 2.0) score += 1.0;

    // Volume factor
    if(currentVolume > avgVolume * 1.5) score += 2.0;
    else if(currentVolume > avgVolume * 1.2) score += 1.0;

    return score;
}

//+------------------------------------------------------------------+
//| Get dynamic R:R ratio based on quality score                    |
//+------------------------------------------------------------------+
double GetDynamicRR(double qualityScore) {
    if(!InpUseEnhancedRR) return InpDynamicRR;

    if(qualityScore >= 4.0) return InpMaxRR;
    else if(qualityScore >= 3.0) return InpMinRR + (InpMaxRR - InpMinRR) * 0.75;
    else if(qualityScore >= 2.0) return InpMinRR + (InpMaxRR - InpMinRR) * 0.5;
    else if(qualityScore >= 1.0) return InpMinRR;
    else return MathMax(1.0, InpMinRR - 0.5);
}

//+------------------------------------------------------------------+
//| Validate entry quality                                           |
//+------------------------------------------------------------------+
bool IsValidEntry(double qualityScore) {
    if(!InpUseEnhancedRR) return true;
    return qualityScore >= InpMinQualityScore;
}

//+------------------------------------------------------------------+
//| Validate volume confirmation                                     |
//+------------------------------------------------------------------+
bool IsVolumeValid() {
    if(!InpRequireVolumeConfirmation) return true;

    long currentVol = iVolume(_Symbol, PERIOD_CURRENT, 0);
    long avgVol = 0;
    for(int i = 1; i <= 20; i++) {
        avgVol += iVolume(_Symbol, PERIOD_CURRENT, i);
    }
    avgVol /= 20;

    return currentVol >= avgVol * 1.1;
}

//+------------------------------------------------------------------+
//| Validate trading time                                            |
//+------------------------------------------------------------------+
bool IsValidTradingTime() {
    if(!InpUseTimeFiltering) return true;

    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentHour = dt.hour;
    int currentDayOfWeek = dt.day_of_week;

    // Weekend filter
    if(InpAvoidWeekends && (currentDayOfWeek == 0 || currentDayOfWeek == 6)) return false;

    // Lunch hours filter
    if(InpAvoidLunchHours && (currentHour >= 12 && currentHour < 13)) return false;

    // Early morning filter
    if(InpAvoidEarlyMorning && (currentHour >= 0 && currentHour < 6)) return false;

    // Major trading session filters
    if(InpTradeLondonOnly && (currentHour < 8 || currentHour >= 16)) return false;
    if(InpTradeNewYorkOnly && (currentHour < 13 || currentHour >= 21)) return false;
    if(InpAvoidAsianSession && (currentHour >= 21 || currentHour < 6)) return false;

    // Day of week filters
    if(InpAvoidMondays && currentDayOfWeek == 1) return false;
    if(InpAvoidFridays && currentDayOfWeek == 5) return false;

    // Session timing filters
    if(InpAvoidFirstHour && (currentHour >= 9 && currentHour < 10)) return false;
    if(InpAvoidLastHour && (currentHour >= 16 && currentHour < 17)) return false;

    // Custom time range filter
    if(InpUseCustomTimeRange) {
        if(InpCustomTimeStart <= InpCustomTimeEnd) {
            if(currentHour < InpCustomTimeStart || currentHour >= InpCustomTimeEnd) return false;
        } else {
            if(currentHour >= InpCustomTimeEnd && currentHour < InpCustomTimeStart) return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Main CRT logic processing                                        |
//+------------------------------------------------------------------+
void ProcessCRTLogic() {
    // Skip if we already have an active position
    if(g_currentCRT.ticket > 0 && PositionSelectByTicket(g_currentCRT.ticket)) {
        return;
    }

    // State machine processing
    if(g_currentCRT.state == "Waiting For Bulky Candle") {
        ProcessWaitingForBulkyCandle();
    }
    else if(g_currentCRT.state == "Waiting For Side Retest") {
        ProcessWaitingForSideRetest();
    }
    else if(g_currentCRT.state == "Waiting For FVG" || g_currentCRT.state == "Waiting For OB") {
        ProcessWaitingForEntry();
    }
    else if(g_currentCRT.state == "Waiting For FVG Retracement" || g_currentCRT.state == "Waiting For OB Retracement") {
        ProcessWaitingForRetracement();
    }
    else if(g_currentCRT.state == "Enter Position") {
        ProcessEnterPosition();
    }
}

//+------------------------------------------------------------------+
//| Process waiting for bulky candle state                          |
//+------------------------------------------------------------------+
void ProcessWaitingForBulkyCandle() {
    if(IsBulkyCandle(1)) {
        g_currentCRT.bulkyHigh = iHigh(_Symbol, PERIOD_CURRENT, 1);
        g_currentCRT.bulkyLow = iLow(_Symbol, PERIOD_CURRENT, 1);
        g_currentCRT.bulkyTimeHigh = iTime(_Symbol, PERIOD_CURRENT, 1);
        g_currentCRT.bulkyTimeLow = iTime(_Symbol, PERIOD_CURRENT, 1);

        // Calculate quality score if enhanced R:R is enabled
        if(InpUseEnhancedRR) {
            double bulkyRange = g_currentCRT.bulkyHigh - g_currentCRT.bulkyLow;
            long volume = iVolume(_Symbol, PERIOD_CURRENT, 1);
            g_currentCRT.qualityScore = CalculateQualityScore(bulkyRange, volume);
            g_currentCRT.dynamicRR = GetDynamicRR(g_currentCRT.qualityScore);
        } else {
            g_currentCRT.qualityScore = 0;
            g_currentCRT.dynamicRR = InpDynamicRR;
        }

        g_currentCRT.state = "Waiting For Side Retest";

        if(InpEnableLogging) {
            Print("Bulky candle detected. Range: ", DoubleToString(g_currentCRT.bulkyHigh - g_currentCRT.bulkyLow, _Digits));
            if(InpUseEnhancedRR) {
                Print("Quality Score: ", DoubleToString(g_currentCRT.qualityScore, 1),
                      " | R:R: ", DoubleToString(g_currentCRT.dynamicRR, 2));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Process waiting for side retest state                           |
//+------------------------------------------------------------------+
void ProcessWaitingForSideRetest() {
    double currentClose = iClose(_Symbol, PERIOD_CURRENT, 0);
    double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);

    // Check for invalidation
    if(currentClose > g_currentCRT.bulkyHigh || currentClose < g_currentCRT.bulkyLow) {
        g_currentCRT.state = "Aborted";
        if(InpEnableLogging) Print("CRT aborted - price moved beyond bulky candle range");
        InitializeCRT();
        return;
    }

    bool bearOverlap = false;
    bool bullOverlap = false;

    // Check for overlaps
    if(currentHigh > g_currentCRT.bulkyHigh && currentClose <= g_currentCRT.bulkyHigh) {
        bearOverlap = true;
    }
    if(currentLow < g_currentCRT.bulkyLow && currentClose >= g_currentCRT.bulkyLow) {
        bullOverlap = true;
    }

    if(bearOverlap && !bullOverlap) {
        g_currentCRT.overlapDirection = "Bear";
        g_currentCRT.breakTime = TimeCurrent();
        if(InpEntryMode == ENTRY_MODE_FVGS) {
            g_currentCRT.state = "Waiting For FVG";
        } else {
            g_currentCRT.state = "Waiting For OB";
        }
        if(InpEnableLogging) Print("Bearish overlap detected");
    }
    else if(bullOverlap && !bearOverlap) {
        g_currentCRT.overlapDirection = "Bull";
        g_currentCRT.breakTime = TimeCurrent();
        if(InpEntryMode == ENTRY_MODE_FVGS) {
            g_currentCRT.state = "Waiting For FVG";
        } else {
            g_currentCRT.state = "Waiting For OB";
        }
        if(InpEnableLogging) Print("Bullish overlap detected");
    }
}

//+------------------------------------------------------------------+
//| Process waiting for entry (FVG/OB detection)                    |
//+------------------------------------------------------------------+
void ProcessWaitingForEntry() {
    // Simplified FVG/OB detection for real-time trading
    // In a full implementation, you would add sophisticated FVG/OB detection
    // For now, we'll use a simplified approach based on price action

    if(InpEntryMode == ENTRY_MODE_FVGS) {
        // Simplified FVG detection
        if(DetectSimplifiedFVG()) {
            if(!InpRequireRetracement) {
                g_currentCRT.state = "Enter Position";
            } else {
                g_currentCRT.state = "Waiting For FVG Retracement";
            }
        }
    } else {
        // Simplified OB detection
        if(DetectSimplifiedOB()) {
            if(!InpRequireRetracement) {
                g_currentCRT.state = "Enter Position";
            } else {
                g_currentCRT.state = "Waiting For OB Retracement";
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Simplified FVG detection                                         |
//+------------------------------------------------------------------+
bool DetectSimplifiedFVG() {
    // Simplified FVG detection based on gap between candles
    double high1 = iHigh(_Symbol, PERIOD_CURRENT, 3);
    double low1 = iLow(_Symbol, PERIOD_CURRENT, 3);
    double high2 = iHigh(_Symbol, PERIOD_CURRENT, 2);
    double low2 = iLow(_Symbol, PERIOD_CURRENT, 2);
    double high3 = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double low3 = iLow(_Symbol, PERIOD_CURRENT, 1);

    if(g_currentCRT.overlapDirection == "Bull") {
        // Bullish FVG: gap between candle 1 high and candle 3 low
        if(low3 > high1) {
            if(InpEnableLogging) Print("Bullish FVG detected");
            return true;
        }
    } else {
        // Bearish FVG: gap between candle 1 low and candle 3 high
        if(high3 < low1) {
            if(InpEnableLogging) Print("Bearish FVG detected");
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Simplified OB detection                                          |
//+------------------------------------------------------------------+
bool DetectSimplifiedOB() {
    // Simplified OB detection based on strong rejection candles
    double open = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close = iClose(_Symbol, PERIOD_CURRENT, 1);
    double high = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double low = iLow(_Symbol, PERIOD_CURRENT, 1);
    double range = high - low;
    double body = MathAbs(close - open);

    if(g_currentCRT.overlapDirection == "Bull") {
        // Bullish OB: strong bullish candle with small upper wick
        if(close > open && body > range * 0.7 && (high - close) < range * 0.2) {
            if(InpEnableLogging) Print("Bullish OB detected");
            return true;
        }
    } else {
        // Bearish OB: strong bearish candle with small lower wick
        if(close < open && body > range * 0.7 && (close - low) < range * 0.2) {
            if(InpEnableLogging) Print("Bearish OB detected");
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Process waiting for retracement                                 |
//+------------------------------------------------------------------+
void ProcessWaitingForRetracement() {
    // Simplified retracement detection
    double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);

    if(g_currentCRT.overlapDirection == "Bull") {
        // For bullish setup, wait for price to retrace into the identified zone
        if(currentLow <= g_currentCRT.bulkyHigh) {
            g_currentCRT.state = "Enter Position";
            if(InpEnableLogging) Print("Bullish retracement completed");
        }
    } else {
        // For bearish setup, wait for price to retrace into the identified zone
        if(currentHigh >= g_currentCRT.bulkyLow) {
            g_currentCRT.state = "Enter Position";
            if(InpEnableLogging) Print("Bearish retracement completed");
        }
    }
}

//+------------------------------------------------------------------+
//| Process enter position state                                    |
//+------------------------------------------------------------------+
void ProcessEnterPosition() {
    // Validate entry conditions
    bool qualityValid = IsValidEntry(g_currentCRT.qualityScore);
    bool volumeValid = IsVolumeValid();
    bool timeValid = IsValidTradingTime();
    bool entryValid = qualityValid && volumeValid && timeValid;

    if(entryValid) {
        if(ExecuteEntry()) {
            if(InpEnableLogging) {
                Print("Entry executed successfully");
                if(InpUseEnhancedRR) {
                    Print("Quality Score: ", DoubleToString(g_currentCRT.qualityScore, 1),
                          " | R:R: ", DoubleToString(g_currentCRT.dynamicRR, 2));
                }
            }
        } else {
            g_currentCRT.state = "Aborted";
            if(InpEnableLogging) Print("Entry execution failed");
            InitializeCRT();
        }
    } else {
        // Log rejection reason
        string rejectionReason = "";
        if(!qualityValid) rejectionReason += "Quality Score below minimum ";
        if(!volumeValid) rejectionReason += "Volume too low ";
        if(!timeValid) rejectionReason += "Time filter active ";

        if(InpEnableLogging) Print("Entry rejected - ", rejectionReason);
        g_currentCRT.state = "Aborted";
        InitializeCRT();
    }
}

//+------------------------------------------------------------------+
//| Execute entry trade                                              |
//+------------------------------------------------------------------+
bool ExecuteEntry() {
    double currentPrice = (g_currentCRT.overlapDirection == "Bull") ? Ask : Bid;
    g_currentCRT.entryPrice = currentPrice;
    g_currentCRT.entryTime = TimeCurrent();
    g_currentCRT.entryType = (g_currentCRT.overlapDirection == "Bull") ? "Long" : "Short";

    // Calculate SL and TP levels
    CalculateTPSLLevels();

    // Calculate lot size
    double lotSize = CalculateLotSize();
    if(lotSize <= 0) {
        if(InpEnableLogging) Print("Invalid lot size calculated");
        return false;
    }

    // Place the trade
    ENUM_ORDER_TYPE orderType = (g_currentCRT.entryType == "Long") ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;

    if(g_trade.PositionOpen(_Symbol, orderType, lotSize, currentPrice, g_currentCRT.slTarget, g_currentCRT.tpTarget, "CRT_EA")) {
        g_currentCRT.ticket = g_trade.ResultOrder();
        g_currentCRT.originalLots = lotSize;
        g_currentCRT.remainingLots = lotSize;
        g_currentCRT.state = "Entry Taken";

        // Setup partial TPs if enabled
        if(InpUsePartialTPs && ValidatePartialTPs()) {
            CalculatePartialLevels();
        }

        // Send alert if enabled
        if(InpEnableAlerts) {
            string alertMsg = "CRT " + g_currentCRT.entryType + " signal on " + _Symbol;
            Alert(alertMsg);
        }

        return true;
    } else {
        if(InpEnableLogging) Print("Trade execution failed: ", g_trade.ResultRetcode());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Calculate TP/SL levels                                           |
//+------------------------------------------------------------------+
void CalculateTPSLLevels() {
    double atr = CalculateATR(1);

    if(InpTPSLMethod == TPSL_FIXED) {
        // Fixed percentage method
        if(g_currentCRT.entryType == "Long") {
            g_currentCRT.slTarget = g_currentCRT.entryPrice * (1 - InpSLPercent / 100.0);
            g_currentCRT.tpTarget = g_currentCRT.entryPrice * (1 + InpTPPercent / 100.0);
        } else {
            g_currentCRT.slTarget = g_currentCRT.entryPrice * (1 + InpSLPercent / 100.0);
            g_currentCRT.tpTarget = g_currentCRT.entryPrice * (1 - InpTPPercent / 100.0);
        }
    } else {
        // Dynamic ATR-based method
        double enhancedRR = InpUseEnhancedRR ? g_currentCRT.dynamicRR : InpDynamicRR;

        if(g_currentCRT.entryType == "Long") {
            g_currentCRT.slTarget = g_currentCRT.entryPrice - atr * InpSLATRMult;
            double riskDistance = MathAbs(g_currentCRT.entryPrice - g_currentCRT.slTarget);
            g_currentCRT.tpTarget = g_currentCRT.entryPrice + riskDistance * enhancedRR;
        } else {
            g_currentCRT.slTarget = g_currentCRT.entryPrice + atr * InpSLATRMult;
            double riskDistance = MathAbs(g_currentCRT.entryPrice - g_currentCRT.slTarget);
            g_currentCRT.tpTarget = g_currentCRT.entryPrice - riskDistance * enhancedRR;
        }
    }

    // Normalize prices
    g_currentCRT.slTarget = NormalizeDouble(g_currentCRT.slTarget, _Digits);
    g_currentCRT.tpTarget = NormalizeDouble(g_currentCRT.tpTarget, _Digits);
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize() {
    if(InpUseFixedLots) {
        return InpLotSize;
    }

    // Risk-based position sizing
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * InpRiskPercent / 100.0;
    double riskDistance = MathAbs(g_currentCRT.entryPrice - g_currentCRT.slTarget);

    if(riskDistance <= 0) return 0;

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double lotSize = riskAmount / (riskDistance / tickSize * tickValue);

    // Normalize lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));

    return lotSize;
}

//+------------------------------------------------------------------+
//| Validate partial TP percentages                                 |
//+------------------------------------------------------------------+
bool ValidatePartialTPs() {
    double finalTPPercent = 100.0 - InpPartialTP1Percent - InpPartialTP2Percent;
    return finalTPPercent > 0;
}

//+------------------------------------------------------------------+
//| Calculate partial TP levels                                     |
//+------------------------------------------------------------------+
void CalculatePartialLevels() {
    double baseDistance = MathAbs(g_currentCRT.entryPrice - g_currentCRT.slTarget);

    if(g_currentCRT.entryType == "Long") {
        // TP1: Always at 1:1 R:R for breakeven protection
        g_currentCRT.partialTP1 = g_currentCRT.entryPrice + baseDistance * 1.0;

        // TP2: Intelligent level based on setup quality
        double tp2Multiplier = (g_currentCRT.qualityScore >= 3.0) ? 1.8 :
                              (g_currentCRT.qualityScore >= 2.0) ? 1.6 : 1.4;
        g_currentCRT.partialTP2 = g_currentCRT.entryPrice + baseDistance * tp2Multiplier;

        // Initialize trailing stop
        g_currentCRT.trailingStop = g_currentCRT.slTarget;
    } else {
        // TP1: Always at 1:1 R:R for breakeven protection
        g_currentCRT.partialTP1 = g_currentCRT.entryPrice - baseDistance * 1.0;

        // TP2: Intelligent level based on setup quality
        double tp2Multiplier = (g_currentCRT.qualityScore >= 3.0) ? 1.8 :
                              (g_currentCRT.qualityScore >= 2.0) ? 1.6 : 1.4;
        g_currentCRT.partialTP2 = g_currentCRT.entryPrice - baseDistance * tp2Multiplier;

        // Initialize trailing stop
        g_currentCRT.trailingStop = g_currentCRT.slTarget;
    }

    // Normalize prices
    g_currentCRT.partialTP1 = NormalizeDouble(g_currentCRT.partialTP1, _Digits);
    g_currentCRT.partialTP2 = NormalizeDouble(g_currentCRT.partialTP2, _Digits);
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManageExistingPositions() {
    if(g_currentCRT.ticket == 0 || g_currentCRT.state != "Entry Taken") return;

    if(!PositionSelectByTicket(g_currentCRT.ticket)) {
        // Position closed externally
        g_currentCRT.state = "Done";
        LogTradeResult();
        InitializeCRT();
        return;
    }

    double currentPrice = (g_currentCRT.entryType == "Long") ? Bid : Ask;

    // Update trailing stop if active
    if(InpUsePartialTPs && ValidatePartialTPs() && g_currentCRT.trailingActive) {
        UpdateTrailingStop(currentPrice);
    }

    // Handle partial profit taking
    if(InpUsePartialTPs && ValidatePartialTPs()) {
        HandlePartialTPs(currentPrice);
    }
}

//+------------------------------------------------------------------+
//| Handle partial take profits                                      |
//+------------------------------------------------------------------+
void HandlePartialTPs(double currentPrice) {
    if(g_currentCRT.entryType == "Long") {
        // Check for Partial TP1 hit
        if(!g_currentCRT.tp1Hit && currentPrice >= g_currentCRT.partialTP1) {
            ExecutePartialClose(1);
        }

        // Check for Partial TP2 hit
        if(g_currentCRT.tp1Hit && !g_currentCRT.tp2Hit && currentPrice >= g_currentCRT.partialTP2) {
            ExecutePartialClose(2);
        }

        // Check for Final TP hit
        if(g_currentCRT.tp2Hit && currentPrice >= g_currentCRT.tpTarget) {
            ExecuteFinalClose("Take Profit");
        }

        // Check for Stop Loss
        double stopLevel = g_currentCRT.trailingActive ? g_currentCRT.trailingStop : g_currentCRT.slTarget;
        if(currentPrice <= stopLevel) {
            ExecuteFinalClose("Stop Loss");
        }
    } else {
        // Short position logic
        // Check for Partial TP1 hit
        if(!g_currentCRT.tp1Hit && currentPrice <= g_currentCRT.partialTP1) {
            ExecutePartialClose(1);
        }

        // Check for Partial TP2 hit
        if(g_currentCRT.tp1Hit && !g_currentCRT.tp2Hit && currentPrice <= g_currentCRT.partialTP2) {
            ExecutePartialClose(2);
        }

        // Check for Final TP hit
        if(g_currentCRT.tp2Hit && currentPrice <= g_currentCRT.tpTarget) {
            ExecuteFinalClose("Take Profit");
        }

        // Check for Stop Loss
        double stopLevel = g_currentCRT.trailingActive ? g_currentCRT.trailingStop : g_currentCRT.slTarget;
        if(currentPrice >= stopLevel) {
            ExecuteFinalClose("Stop Loss");
        }
    }
}

//+------------------------------------------------------------------+
//| Execute partial close                                            |
//+------------------------------------------------------------------+
void ExecutePartialClose(int tpLevel) {
    double closePercent = (tpLevel == 1) ? InpPartialTP1Percent : InpPartialTP2Percent;
    double closeLots = g_currentCRT.originalLots * closePercent / 100.0;

    // Normalize lot size
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    closeLots = MathRound(closeLots / lotStep) * lotStep;

    if(closeLots > 0 && closeLots <= g_currentCRT.remainingLots) {
        if(g_trade.PositionClosePartial(g_currentCRT.ticket, closeLots)) {
            g_currentCRT.remainingLots -= closeLots;
            g_currentCRT.remainingPosition -= closePercent;

            if(tpLevel == 1) {
                g_currentCRT.tp1Hit = true;
                g_currentCRT.tp1HitTime = TimeCurrent();

                // Move to breakeven after first partial
                if(InpUseTrailingStop) {
                    g_currentCRT.trailingStop = g_currentCRT.entryPrice;
                    g_currentCRT.trailingActive = true;
                }

                if(InpEnableLogging) {
                    Print("Partial TP1 Hit - ", DoubleToString(closePercent, 0), "% closed, ",
                          DoubleToString(g_currentCRT.remainingPosition, 0), "% remaining");
                }
            } else {
                g_currentCRT.tp2Hit = true;
                g_currentCRT.tp2HitTime = TimeCurrent();

                if(InpEnableLogging) {
                    Print("Partial TP2 Hit - ", DoubleToString(closePercent, 0), "% closed, ",
                          DoubleToString(g_currentCRT.remainingPosition, 0), "% remaining");
                }
            }
        } else {
            if(InpEnableLogging) Print("Partial close failed: ", g_trade.ResultRetcode());
        }
    }
}

//+------------------------------------------------------------------+
//| Execute final close                                              |
//+------------------------------------------------------------------+
void ExecuteFinalClose(string reason) {
    if(g_trade.PositionClose(g_currentCRT.ticket)) {
        g_currentCRT.exitPrice = (g_currentCRT.entryType == "Long") ? Bid : Ask;
        g_currentCRT.exitTime = TimeCurrent();
        g_currentCRT.state = reason;
        g_currentCRT.remainingPosition = 0.0;
        g_currentCRT.remainingLots = 0.0;

        if(InpEnableLogging) {
            Print("Position closed - ", reason, " at ", DoubleToString(g_currentCRT.exitPrice, _Digits));
        }

        // Calculate and log trade result
        LogTradeResult();

        // Send alert if enabled
        if(InpEnableAlerts) {
            string alertMsg = "CRT " + reason + " on " + _Symbol + " at " + DoubleToString(g_currentCRT.exitPrice, _Digits);
            Alert(alertMsg);
        }

        // Reset for next trade
        InitializeCRT();
    } else {
        if(InpEnableLogging) Print("Position close failed: ", g_trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Update trailing stop                                             |
//+------------------------------------------------------------------+
void UpdateTrailingStop(double currentPrice) {
    if(!InpUseTrailingStop || !g_currentCRT.trailingActive) return;

    double atr = CalculateATR(0);
    double newTrailing = g_currentCRT.trailingStop;

    if(g_currentCRT.entryType == "Long") {
        // Trail stop below recent highs
        newTrailing = currentPrice - atr * InpTrailingATRMult;
        // Only move trailing stop up, never down
        if(newTrailing > g_currentCRT.trailingStop) {
            g_currentCRT.trailingStop = NormalizeDouble(newTrailing, _Digits);

            // Modify the position's stop loss
            if(g_trade.PositionModify(g_currentCRT.ticket, g_currentCRT.trailingStop, g_currentCRT.tpTarget)) {
                if(InpEnableLogging) {
                    Print("Trailing stop updated to: ", DoubleToString(g_currentCRT.trailingStop, _Digits));
                }
            }
        }
    } else {
        // Trail stop above recent lows
        newTrailing = currentPrice + atr * InpTrailingATRMult;
        // Only move trailing stop down, never up
        if(newTrailing < g_currentCRT.trailingStop) {
            g_currentCRT.trailingStop = NormalizeDouble(newTrailing, _Digits);

            // Modify the position's stop loss
            if(g_trade.PositionModify(g_currentCRT.ticket, g_currentCRT.trailingStop, g_currentCRT.tpTarget)) {
                if(InpEnableLogging) {
                    Print("Trailing stop updated to: ", DoubleToString(g_currentCRT.trailingStop, _Digits));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate R:R ratio for completed trade                         |
//+------------------------------------------------------------------+
double CalculateRR() {
    if(g_currentCRT.entryPrice == 0 || g_currentCRT.slTarget == 0 || g_currentCRT.exitPrice == 0) {
        return 0;
    }

    double risk = MathAbs(g_currentCRT.entryPrice - g_currentCRT.slTarget);
    double reward = g_currentCRT.exitPrice - g_currentCRT.entryPrice;

    // For short trades, invert the reward calculation
    if(g_currentCRT.entryType == "Short") {
        reward = g_currentCRT.entryPrice - g_currentCRT.exitPrice;
    }

    return (risk > 0) ? reward / risk : 0;
}

//+------------------------------------------------------------------+
//| Log trade result and update statistics                          |
//+------------------------------------------------------------------+
void LogTradeResult() {
    double actualRR = CalculateRR();
    g_currentCRT.actualRR = actualRR;

    // Determine trade result
    if(actualRR > 0.05) {
        g_currentCRT.tradeResult = "Win";
        g_winningTrades++;
    } else if(actualRR < -0.05) {
        g_currentCRT.tradeResult = "Loss";
        g_losingTrades++;
    } else {
        g_currentCRT.tradeResult = "Breakeven";
    }

    g_totalTrades++;
    g_totalRR += actualRR;

    // Log trade details
    if(InpEnableLogging) {
        Print("=== Trade Completed ===");
        Print("Type: ", g_currentCRT.entryType);
        Print("Entry: ", DoubleToString(g_currentCRT.entryPrice, _Digits));
        Print("Exit: ", DoubleToString(g_currentCRT.exitPrice, _Digits));
        Print("Result: ", g_currentCRT.tradeResult);
        Print("R:R: ", DoubleToString(actualRR, 2), "R");
        Print("Total Trades: ", g_totalTrades);
        Print("Win Rate: ", DoubleToString(g_winningTrades * 100.0 / g_totalTrades, 1), "%");
        Print("Average R:R: ", DoubleToString(g_totalRR / g_totalTrades, 2), "R");
        Print("======================");
    }

    // Write to file if logging enabled
    if(InpLogTrades) {
        WriteTradeToFile();
    }
}

//+------------------------------------------------------------------+
//| Write trade details to CSV file                                 |
//+------------------------------------------------------------------+
void WriteTradeToFile() {
    string filename = "CRT_EA_Trades_" + _Symbol + ".csv";
    int handle = FileOpen(filename, FILE_WRITE|FILE_CSV|FILE_COMMON);

    if(handle != INVALID_HANDLE) {
        // Write header if file is new
        if(FileSize(handle) == 0) {
            FileWrite(handle, "Date", "Time", "Symbol", "Type", "Entry", "Exit", "SL", "TP",
                     "Result", "RR", "QualityScore", "DynamicRR");
        }

        // Write trade data
        MqlDateTime dt;
        TimeToStruct(g_currentCRT.entryTime, dt);
        string dateStr = StringFormat("%04d.%02d.%02d", dt.year, dt.mon, dt.day);
        string timeStr = StringFormat("%02d:%02d", dt.hour, dt.min);

        FileWrite(handle, dateStr, timeStr, _Symbol, g_currentCRT.entryType,
                 DoubleToString(g_currentCRT.entryPrice, _Digits),
                 DoubleToString(g_currentCRT.exitPrice, _Digits),
                 DoubleToString(g_currentCRT.slTarget, _Digits),
                 DoubleToString(g_currentCRT.tpTarget, _Digits),
                 g_currentCRT.tradeResult,
                 DoubleToString(g_currentCRT.actualRR, 2),
                 DoubleToString(g_currentCRT.qualityScore, 1),
                 DoubleToString(g_currentCRT.dynamicRR, 2));

        FileClose(handle);
    } else {
        if(InpEnableLogging) Print("Failed to open trade log file: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Display current statistics on chart                             |
//+------------------------------------------------------------------+
void DisplayStatistics() {
    if(g_totalTrades == 0) return;

    string stats = "CRT EA Statistics\n";
    stats += "Total Trades: " + IntegerToString(g_totalTrades) + "\n";
    stats += "Wins: " + IntegerToString(g_winningTrades) + "\n";
    stats += "Losses: " + IntegerToString(g_losingTrades) + "\n";
    stats += "Win Rate: " + DoubleToString(g_winningTrades * 100.0 / g_totalTrades, 1) + "%\n";
    stats += "Total R:R: " + DoubleToString(g_totalRR, 2) + "R\n";
    stats += "Average R:R: " + DoubleToString(g_totalRR / g_totalTrades, 2) + "R\n";

    // Add current CRT state
    stats += "\nCurrent State: " + g_currentCRT.state;
    if(InpUseEnhancedRR && g_currentCRT.qualityScore > 0) {
        stats += "\nQuality Score: " + DoubleToString(g_currentCRT.qualityScore, 1);
        stats += "\nDynamic R:R: " + DoubleToString(g_currentCRT.dynamicRR, 2);
    }

    Comment(stats);
}

//+------------------------------------------------------------------+
//| Timer function for periodic updates                             |
//+------------------------------------------------------------------+
void OnTimer() {
    DisplayStatistics();
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam) {
    if(id == CHARTEVENT_CHART_CHANGE) {
        DisplayStatistics();
    }
}

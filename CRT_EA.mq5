//+------------------------------------------------------------------+
//| CRT_EA.mq5                                                       |
//| Candle Range Theory Expert Advisor                               |
//| Converted from TradingView Pine Script Indicator                 |
//+------------------------------------------------------------------+
#property copyright "CRT Strategy - Converted from TradingView"
#property link      "https://github.com/your-repo/crt-ea"
#property version   "1.00"
#property description "Candle Range Theory EA - Identical logic to TradingView indicator"
#property strict

//+------------------------------------------------------------------+
//| Includes                                                         |
//+------------------------------------------------------------------+
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\DealInfo.mqh>

//+------------------------------------------------------------------+
//| Core Data Structures                                            |
//+------------------------------------------------------------------+

// FVG Information Structure (equivalent to FVGInfo type)
struct SFVGInfo
{
   double            max;                    // FVG top level
   double            min;                    // FVG bottom level
   bool              isBull;                 // Bullish FVG flag
   datetime          startTime;              // FVG start time
   double            totalVolume;            // Total volume
   int               startBarIndex;          // Start bar index
   int               endBarIndex;            // End bar index
   datetime          endTime;                // End time
   bool              extendInfinite;         // Extend infinite flag
   bool              combined;               // Combined flag
   string            combinedTimeframesStr;  // Combined timeframes
   bool              disabled;               // Disabled flag
   string            timeframeStr;           // Timeframe string
   double            lowVolume;              // Low volume
   double            highVolume;             // High volume
   bool              isInverse;              // Inverse FVG flag
   datetime          lastTouched;            // Last touched time
   datetime          lastTouchedIFVG;        // Last touched IFVG time
   int               inverseEndIndex;        // Inverse end index
   datetime          inverseEndTime;         // Inverse end time
   double            inverseVolume;          // Inverse volume
};

// Order Block Information Structure (equivalent to orderBlockInfo type)
struct SOrderBlockInfo
{
   double            top;                    // Order block top
   double            bottom;                 // Order block bottom
   double            obVolume;               // Order block volume
   string            obType;                 // Order block type
   datetime          startTime;              // Start time
   double            bbVolume;               // Breaker block volume
   double            obLowVolume;            // OB low volume
   double            obHighVolume;           // OB high volume
   bool              breaker;                // Breaker flag
   datetime          breakTime;              // Break time
   datetime          breakerEndTime;         // Breaker end time
   string            timeframeStr;           // Timeframe string
   bool              disabled;               // Disabled flag
   string            combinedTimeframesStr;  // Combined timeframes
   bool              combined;               // Combined flag
};

//+------------------------------------------------------------------+
//| Enumerations (MOVED BEFORE STRUCTURES)                          |
//+------------------------------------------------------------------+
enum ENUM_CRT_STATE
{
   CRT_WAITING_FOR_BULKY_CANDLE,     // Waiting For Bulky Candle
   CRT_WAITING_FOR_SIDE_RETEST,      // Waiting For Side Retest
   CRT_WAITING_FOR_FVG,              // Waiting For FVG
   CRT_WAITING_FOR_OB,               // Waiting For OB
   CRT_WAITING_FOR_FVG_RETRACEMENT,  // Waiting For FVG Retracement
   CRT_WAITING_FOR_OB_RETRACEMENT,   // Waiting For OB Retracement
   CRT_ENTER_POSITION,               // Enter Position
   CRT_ENTRY_TAKEN,                  // Entry Taken
   CRT_STOP_LOSS,                    // Stop Loss
   CRT_TAKE_PROFIT,                  // Take Profit
   CRT_ABORTED,                      // Aborted
   CRT_DONE                          // Done
};

enum ENUM_OVERLAP_DIRECTION
{
   OVERLAP_BULL,                    // Bull Overlap
   OVERLAP_BEAR,                    // Bear Overlap
   OVERLAP_NONE                     // No Overlap
};

enum ENUM_ENTRY_MODE
{
   ENTRY_FVGS,                      // FVGs
   ENTRY_ORDER_BLOCKS               // Order Blocks
};

enum ENUM_TPSL_METHOD
{
   TPSL_DYNAMIC,                    // Dynamic
   TPSL_FIXED                       // Fixed
};

enum ENUM_RISK_AMOUNT
{
   RISK_HIGHEST,                    // Highest
   RISK_HIGH,                       // High
   RISK_NORMAL,                     // Normal
   RISK_LOW,                        // Low
   RISK_LOWEST                      // Lowest
};

enum ENUM_CANDLE_SIZE
{
   CANDLE_BIG,                      // Big
   CANDLE_NORMAL,                   // Normal
   CANDLE_SMALL                     // Small
};

enum ENUM_FVG_SENSITIVITY
{
   FVG_ALL,                         // All (100)
   FVG_EXTREME,                     // Extreme (6)
   FVG_HIGH,                        // High (2)
   FVG_NORMAL,                      // Normal (1.5)
   FVG_LOW                          // Low (1)
};

// Main CRT Setup Structure (equivalent to CRT type)
struct SCRTSetup
{
   ENUM_CRT_STATE    state;                  // Current state
   datetime          startTime;              // Setup start time

   ENUM_OVERLAP_DIRECTION overlapDirection;  // Overlap direction
   datetime          bulkyTimeLow;           // Bulky candle low time
   datetime          bulkyTimeHigh;          // Bulky candle high time
   double            bulkyHigh;              // Bulky candle high
   double            bulkyLow;               // Bulky candle low
   datetime          breakTime;              // Break time

   SFVGInfo          fvg;                    // FVG information
   datetime          fvgEndTime;             // FVG end time
   SOrderBlockInfo   ob;                     // Order block information

   double            slTarget;               // Stop loss target
   double            tpTarget;               // Take profit target
   string            entryType;              // Entry type (Long/Short) - REVERTED: back to string
   datetime          entryTime;              // Entry time
   datetime          exitTime;               // Exit time
   double            entryPrice;             // Entry price
   double            exitPrice;              // Exit price
   int               dayEndedBeforeExit;     // Day ended before exit

   // Enhanced R:R fields
   double            qualityScore;           // Setup quality score
   double            dynamicRR;              // Dynamic R:R ratio

   // REMOVED: Partial Profit Taking fields (user request)

   // Win/Loss Visual System fields
   string            tradeResult;            // Trade result (Win/Loss/Breakeven) - REVERTED: back to string
   double            actualRR;               // Actual R:R achieved
   bool              showResult;             // Show result flag

   // Trade Execution fields
   ulong             positionTicket;         // Position ticket number
   double            positionSize;           // Position size (lots)
   ulong             orderTickets[10];       // Order tickets array
   int               orderCount;             // Number of orders
};

// Bar Information Structure (equivalent to barInfo type)
struct SBarInfo
{
   double            open;                   // Open price
   double            high;                   // High price
   double            low;                    // Low price
   double            close;                  // Close price
   double            trueRange;              // True range
   double            atr;                    // ATR value
};

// REMOVED: Duplicate enum definitions (moved before structures)

//+------------------------------------------------------------------+
//| Input Parameters - General Configuration                         |
//+------------------------------------------------------------------+
input group "General Configuration"
input ENUM_TIMEFRAMES    InpHigherTF = PERIOD_H4;                    // Higher Timeframe
input ENUM_CANDLE_SIZE   InpBulkyCandleSize = CANDLE_BIG;           // HTF Candle Size
input ENUM_ENTRY_MODE    InpEntryMode = ENTRY_FVGS;                 // Entry Mode
input bool               InpRequireRetracement = false;              // Require Retracement
input bool               InpShowHTFLines = true;                     // Show HTF Candle Lines
input int                InpATRLength = 10;                          // ATR Length (FIXED: was 50, indicator uses 10)

//+------------------------------------------------------------------+
//| Input Parameters - TP/SL Configuration                          |
//+------------------------------------------------------------------+
input group "TP / SL"
input bool               InpShowTPSL = true;                         // Enabled
input ENUM_TPSL_METHOD   InpTPSLMethod = TPSL_DYNAMIC;              // TP / SL Method
input ENUM_RISK_AMOUNT   InpRiskAmount = RISK_HIGH;                 // Dynamic Risk
input double             InpTPPercent = 0.3;                        // Fixed Take Profit %
input double             InpSLPercent = 0.4;                        // Fixed Stop Loss %

//+------------------------------------------------------------------+
//| Input Parameters - Enhanced R:R System                          |
//+------------------------------------------------------------------+
input group "Enhanced R:R"
input bool               InpUseEnhancedRR = false;                   // Enable Enhanced R:R System
input double             InpMinRR = 1.5;                            // Minimum R:R Ratio (1.0-6.0)
input double             InpMaxRR = 2.5;                            // Maximum R:R Ratio (1.5-10.0)
input double             InpMinQualityScore = 2.0;                   // Minimum Quality Score (0.0-5.0)
input bool               InpRequireVolumeConfirmation = false;       // Require Volume Confirmation

// REMOVED: Partial Profit Taking parameters (user request)

//+------------------------------------------------------------------+
//| Input Parameters - Time-Based Filtering                         |
//+------------------------------------------------------------------+
input group "Time Filtering"
input bool               InpUseTimeFiltering = false;                // Enable Time Filtering
input bool               InpAvoidWeekends = false;                   // Avoid Weekends
input bool               InpAvoidLunchHours = false;                 // Avoid Lunch Hours (12:00-13:00)
input bool               InpAvoidEarlyMorning = false;               // Avoid Early Morning (00:00-06:00)
input bool               InpUseCustomTimeRange = false;              // Use Custom Time Range
input int                InpCustomTimeStart = 9;                     // Trading Start Hour (0-23)
input int                InpCustomTimeEnd = 17;                      // Trading End Hour (0-23)
input bool               InpTradeLondonOnly = false;                 // Trade London Session Only (08:00-16:00)
input bool               InpTradeNewYorkOnly = false;                // Trade New York Session Only (13:00-21:00)
input bool               InpAvoidAsianSession = false;               // Avoid Asian Session (21:00-06:00)
input bool               InpAvoidMondays = false;                    // Avoid Mondays
input bool               InpAvoidFridays = false;                    // Avoid Fridays
input bool               InpAvoidFirstHour = false;                  // Avoid First Hour (09:00-10:00)
input bool               InpAvoidLastHour = false;                   // Avoid Last Hour (16:00-17:00)

//+------------------------------------------------------------------+
//| Input Parameters - FVG Detection                                |
//+------------------------------------------------------------------+
input group "Fair Value Gaps"
input ENUM_FVG_SENSITIVITY InpFVGSensitivity = FVG_HIGH;            // FVG Detection Sensitivity (ADDED: was missing)
input bool               InpShowFVG = true;                         // Show FVGs (ADDED: was missing)

//+------------------------------------------------------------------+
//| Input Parameters - Order Block Detection                        |
//+------------------------------------------------------------------+
input group "Order Blocks"
input int                InpSwingLength = 10;                       // Swing Length (3-45) (ADDED: was missing)
input bool               InpShowOB = true;                          // Show Order Blocks (ADDED: was missing)

//+------------------------------------------------------------------+
//| Input Parameters - Visual Enhancements                          |
//+------------------------------------------------------------------+
input group "Visual Enhancements"
input bool               InpShowWinLossMarkers = true;               // Show Win/Loss Markers
input bool               InpShowPositionProjection = true;           // Show Position Projection

//+------------------------------------------------------------------+
//| Input Parameters - Risk Management                              |
//+------------------------------------------------------------------+
input group "Risk Management"
input double             InpLotSize = 0.01;                         // Lot Size
input double             InpMaxRiskPercent = 2.0;                    // Maximum Risk Per Trade %
input int                InpMaxConcurrentTrades = 1;                // Maximum Concurrent Trades
input bool               InpUseFixedLots = true;                     // Use Fixed Lot Size
input int                InpMagicNumber = 123456;                    // Magic Number

//+------------------------------------------------------------------+
//| Input Parameters - Additional Filters                           |
//+------------------------------------------------------------------+
input group "Additional Filters"
input bool               InpUseTimeFilter = false;                   // Use Time Filter
input bool               InpUseVolumeFilter = false;                 // Use Volume Filter

//+------------------------------------------------------------------+
//| Input Parameters - Alerts                                       |
//+------------------------------------------------------------------+
input group "Alerts"
input bool               InpBuyAlertEnabled = true;                  // Buy Signal
input bool               InpSellAlertEnabled = true;                 // Sell Signal
input bool               InpTPAlertEnabled = true;                   // Take-Profit Signal
input bool               InpSLAlertEnabled = true;                   // Stop-Loss Signal

//+------------------------------------------------------------------+
//| FVG Detection Class                                             |
//+------------------------------------------------------------------+
class CFVGDetector
{
private:
   SFVGInfo          m_activeFVGs[];           // Active FVGs array
   int               m_maxFVGs;                // Maximum FVGs to track
   double            m_fvgSensitivity;         // FVG sensitivity multiplier
   // REMOVED: Volume SMA indicators (not used in actual FVG detection logic)
   bool              m_initialized;            // Initialization flag

public:
   // Constructor
   CFVGDetector(int maxFVGs = 50)
   {
      m_maxFVGs = maxFVGs;
      m_fvgSensitivity = 2.0; // Default "High" sensitivity
      m_initialized = false;
      ArrayResize(m_activeFVGs, 0);
      // REMOVED: Volume SMA buffer initialization (not used in FVG detection)
   }

   // Destructor
   ~CFVGDetector()
   {
      // REMOVED: Volume SMA handle cleanup (not used in FVG detection)
   }

   // Initialize FVG detector
   bool Initialize()
   {
      // REMOVED: Volume SMA indicators (not used in actual FVG detection logic)
      // The CheckVolumeCondition() function only uses candle body sizes and ATR

      // Set FVG sensitivity based on input (FIXED: Use exact indicator values)
      switch(InpFVGSensitivity)
      {
         case FVG_ALL:
            m_fvgSensitivity = 100.0;
            break;
         case FVG_EXTREME:
            m_fvgSensitivity = 6.0;
            break;
         case FVG_HIGH:
            m_fvgSensitivity = 2.0;
            break;
         case FVG_NORMAL:
            m_fvgSensitivity = 1.5;
            break;
         case FVG_LOW:
            m_fvgSensitivity = 1.0;
            break;
         default:
            m_fvgSensitivity = 2.0; // Default to High
            break;
      }

      m_initialized = true;
      LogInfo("FVG Detector initialized successfully");
      return true;
   }

   // REMOVED: UpdateVolumeData function (volume indicators not used in FVG detection)

   // Detect new FVGs (main detection function)
   bool DetectFVGs()
   {
      if(!m_initialized)
         return false;

      // REMOVED: Volume data update (not used in FVG detection logic)

      // Get current ATR for size validation
      double currentATR = GetCurrentATR();
      if(currentATR <= 0)
         return false;

      // Check volume conditions
      bool bearCondition = CheckVolumeCondition();
      bool bullCondition = bearCondition; // Same condition for both

      // Get OHLC data for 3-candle pattern
      double high0 = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double low0 = iLow(_Symbol, PERIOD_CURRENT, 0);
      double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
      double high2 = iHigh(_Symbol, PERIOD_CURRENT, 2);
      double low2 = iLow(_Symbol, PERIOD_CURRENT, 2);

      // Pine Script FVG detection logic
      // bearFVG = high < low[2] and close[1] < low[2] and bearCondition
      bool bearFVG = (high0 < low2) && (close1 < low2) && bearCondition;

      // bullFVG = low > high[2] and close[1] > high[2] and bullCondition
      bool bullFVG = (low0 > high2) && (close1 > high2) && bullCondition;

      // Create FVGs if detected
      bool newFVGDetected = false;

      if(bearFVG)
      {
         double fvgSize = MathAbs(low2 - high0);
         if(ValidateFVGSize(fvgSize, currentATR))
         {
            CreateFVG(low2, high0, false, TimeCurrent()); // Bearish FVG
            newFVGDetected = true;
            LogInfo("Bearish FVG detected - Top: " + DoubleToString(low2, _Digits) +
                    ", Bottom: " + DoubleToString(high0, _Digits));
         }
      }

      if(bullFVG)
      {
         double fvgSize = MathAbs(low0 - high2);
         if(ValidateFVGSize(fvgSize, currentATR))
         {
            CreateFVG(low0, high2, true, TimeCurrent()); // Bullish FVG
            newFVGDetected = true;
            LogInfo("Bullish FVG detected - Top: " + DoubleToString(low0, _Digits) +
                    ", Bottom: " + DoubleToString(high2, _Digits));
         }
      }

      // Update existing FVGs (check for invalidation)
      UpdateActiveFVGs();

      return newFVGDetected;
   }

   // Get latest FVG
   SFVGInfo GetLatestFVG(bool bullish = true)
   {
      SFVGInfo emptyFVG = {};

      for(int i = 0; i < ArraySize(m_activeFVGs); i++)
      {
         if(m_activeFVGs[i].isBull == bullish && m_activeFVGs[i].endTime == 0)
         {
            return m_activeFVGs[i];
         }
      }

      return emptyFVG;
   }

   // Check if FVG detector is ready
   bool IsReady() const { return m_initialized; }

private:
   // Check volume condition (CRITICAL FIX: Exact Pine Script implementation)
   bool CheckVolumeCondition()
   {
      // Get current ATR
      double currentATR = GetCurrentATR();
      if(currentATR <= 0)
         return false;

      // CRITICAL FIX: Pine Script uses candle BODY size, not range
      // firstBarSize = math.max(open, close) - math.min(open, close)
      // secondBarSize = math.max(open[1], close[1]) - math.min(open[1], close[1])
      // thirdBarSize = math.max(open[2], close[2]) - math.min(open[2], close[2])

      double open0 = iOpen(_Symbol, PERIOD_CURRENT, 0);
      double close0 = iClose(_Symbol, PERIOD_CURRENT, 0);
      double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
      double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
      double open2 = iOpen(_Symbol, PERIOD_CURRENT, 2);
      double close2 = iClose(_Symbol, PERIOD_CURRENT, 2);

      double firstBarSize = MathAbs(MathMax(open0, close0) - MathMin(open0, close0));
      double secondBarSize = MathAbs(MathMax(open1, close1) - MathMin(open1, close1));
      double thirdBarSize = MathAbs(MathMax(open2, close2) - MathMin(open2, close2));
      double barSizeSum = firstBarSize + secondBarSize + thirdBarSize;

      // Pine Script condition: barSizeSum * fvgSensitivity > atr / 1.5
      return (barSizeSum * m_fvgSensitivity > currentATR / 1.5);
   }

   // Validate FVG size (equivalent to Pine Script FVGSizeEnough)
   bool ValidateFVGSize(double fvgSize, double atr)
   {
      // Pine Script: FVGSizeEnough = (FVGSize * fvgSensitivity > atr)
      return (fvgSize * m_fvgSensitivity > atr);
   }

   // Create new FVG
   void CreateFVG(double max, double min, bool isBull, datetime startTime)
   {
      // Resize array if needed
      int currentSize = ArraySize(m_activeFVGs);
      if(currentSize >= m_maxFVGs)
      {
         // Remove oldest FVG
         for(int i = 0; i < currentSize - 1; i++)
         {
            m_activeFVGs[i] = m_activeFVGs[i + 1];
         }
         ArrayResize(m_activeFVGs, currentSize - 1);
      }

      // Add new FVG
      int newSize = ArraySize(m_activeFVGs) + 1;
      ArrayResize(m_activeFVGs, newSize);

      SFVGInfo newFVG = {};
      newFVG.max = max;
      newFVG.min = min;
      newFVG.isBull = isBull;
      newFVG.startTime = startTime;
      newFVG.endTime = 0; // Active FVG
      newFVG.totalVolume = CalculateFVGVolume();

      m_activeFVGs[newSize - 1] = newFVG;
   }

   // Update active FVGs (check for invalidation)
   void UpdateActiveFVGs()
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);

      for(int i = 0; i < ArraySize(m_activeFVGs); i++)
      {
         if(m_activeFVGs[i].endTime != 0)
            continue; // Already invalidated

         // Check FVG invalidation
         // Bullish FVG: invalidated when price touches top (low <= max)
         // Bearish FVG: invalidated when price touches bottom (high >= min)
         if(m_activeFVGs[i].isBull && currentLow <= m_activeFVGs[i].max)
         {
            m_activeFVGs[i].endTime = TimeCurrent();
            LogDebug("Bullish FVG invalidated at " + DoubleToString(m_activeFVGs[i].max, _Digits));
         }
         else if(!m_activeFVGs[i].isBull && currentHigh >= m_activeFVGs[i].min)
         {
            m_activeFVGs[i].endTime = TimeCurrent();
            LogDebug("Bearish FVG invalidated at " + DoubleToString(m_activeFVGs[i].min, _Digits));
         }
      }
   }

   // Calculate FVG volume (3-bar sum)
   double CalculateFVGVolume()
   {
      long vol0 = iVolume(_Symbol, PERIOD_CURRENT, 0);
      long vol1 = iVolume(_Symbol, PERIOD_CURRENT, 1);
      long vol2 = iVolume(_Symbol, PERIOD_CURRENT, 2);

      return (double)(vol0 + vol1 + vol2);
   }

   // Wait for indicator to be ready
   bool WaitForIndicator(int handle)
   {
      int attempts = 0;
      while(BarsCalculated(handle) < 2 && attempts < 100)
      {
         Sleep(50);
         attempts++;
      }
      return BarsCalculated(handle) >= 2;
   }
};

//+------------------------------------------------------------------+
//| Order Block Detection Class                                     |
//+------------------------------------------------------------------+
class COrderBlockDetector
{
private:
   SOrderBlockInfo   m_activeOBs[];             // Active Order Blocks array
   int               m_maxOBs;                   // Maximum OBs to track
   int               m_swingLength;              // Swing detection length
   double            m_maxATRMult;               // Maximum ATR multiplier for OB size
   bool              m_initialized;              // Initialization flag

   // Swing tracking variables
   int               m_swingType;                // Current swing type (0=high, 1=low)
   double            m_topSwingY;                // Top swing price
   int               m_topSwingX;                // Top swing bar index
   bool              m_topSwingCrossed;          // Top swing crossed flag
   double            m_btmSwingY;                // Bottom swing price
   int               m_btmSwingX;                // Bottom swing bar index
   bool              m_btmSwingCrossed;          // Bottom swing crossed flag

public:
   // Constructor (FIXED: Use InpSwingLength parameter)
   COrderBlockDetector(int maxOBs = 50)
   {
      m_maxOBs = maxOBs;
      m_swingLength = InpSwingLength; // Use input parameter
      m_maxATRMult = 3.5; // FIXED: Use indicator value (was 3.0)
      m_initialized = false;
      m_swingType = 0;
      m_topSwingY = 0;
      m_topSwingX = 0;
      m_topSwingCrossed = false;
      m_btmSwingY = 0;
      m_btmSwingX = 0;
      m_btmSwingCrossed = false;
      ArrayResize(m_activeOBs, 0);
   }

   // Destructor
   ~COrderBlockDetector()
   {
      // No special cleanup needed
   }

   // Initialize Order Block detector
   bool Initialize()
   {
      m_initialized = true;
      LogInfo("Order Block Detector initialized successfully");
      return true;
   }

   // Detect new Order Blocks (main detection function)
   bool DetectOrderBlocks()
   {
      if(!m_initialized)
         return false;

      // Get current ATR for size validation
      double currentATR = GetCurrentATR();
      if(currentATR <= 0)
         return false;

      // Update swing detection
      UpdateSwingDetection();

      // Check for Order Block formation
      bool newOBDetected = false;

      // Check for bullish Order Block (break above swing high)
      double currentClose = iClose(_Symbol, PERIOD_CURRENT, 0);
      if(currentClose > m_topSwingY && !m_topSwingCrossed)
      {
         m_topSwingCrossed = true;
         if(CreateBullishOB(currentATR))
         {
            newOBDetected = true;
            LogInfo("Bullish Order Block detected at swing high: " + DoubleToString(m_topSwingY, _Digits));
         }
      }

      // Check for bearish Order Block (break below swing low)
      if(currentClose < m_btmSwingY && !m_btmSwingCrossed)
      {
         m_btmSwingCrossed = true;
         if(CreateBearishOB(currentATR))
         {
            newOBDetected = true;
            LogInfo("Bearish Order Block detected at swing low: " + DoubleToString(m_btmSwingY, _Digits));
         }
      }

      // Update existing Order Blocks (check for invalidation)
      UpdateActiveOBs();

      return newOBDetected;
   }

   // Get latest Order Block
   SOrderBlockInfo GetLatestOB(bool bullish = true)
   {
      SOrderBlockInfo emptyOB = {};

      for(int i = 0; i < ArraySize(m_activeOBs); i++)
      {
         bool isBullishOB = (m_activeOBs[i].obType == "Bull");
         if(isBullishOB == bullish && !m_activeOBs[i].breaker)
         {
            return m_activeOBs[i];
         }
      }

      return emptyOB;
   }

   // Check if Order Block detector is ready
   bool IsReady() const { return m_initialized; }

private:
   // Update swing detection (equivalent to Pine Script swing logic)
   void UpdateSwingDetection()
   {
      // Get highest and lowest values over swing length
      double upper = 0, lower = 0;

      // Calculate highest high and lowest low over swing period
      for(int i = 0; i < m_swingLength; i++)
      {
         double high_i = iHigh(_Symbol, PERIOD_CURRENT, i);
         double low_i = iLow(_Symbol, PERIOD_CURRENT, i);

         if(i == 0 || high_i > upper)
            upper = high_i;
         if(i == 0 || low_i < lower)
            lower = low_i;
      }

      // Get swing center values
      double hi = iHigh(_Symbol, PERIOD_CURRENT, m_swingLength);
      double li = iLow(_Symbol, PERIOD_CURRENT, m_swingLength);
      int bi = m_swingLength; // Bar index offset

      // Determine swing type
      int newSwingType = (hi > upper) ? 0 : (li < lower) ? 1 : m_swingType;

      // Update swing points when swing type changes
      if(newSwingType == 0 && m_swingType != 0) // New high swing
      {
         m_topSwingY = hi;
         m_topSwingX = bi;
         m_topSwingCrossed = false;
      }

      if(newSwingType == 1 && m_swingType != 1) // New low swing
      {
         m_btmSwingY = li;
         m_btmSwingX = bi;
         m_btmSwingCrossed = false;
      }

      m_swingType = newSwingType;
   }

   // Create bullish Order Block
   bool CreateBullishOB(double atr)
   {
      // Calculate Order Block boundaries (3-bar formation)
      double boxTop = 0, boxBottom = 0;
      datetime boxTime = 0;

      // Find the formation bars around the swing
      for(int i = 0; i < 3; i++)
      {
         double high_i = iHigh(_Symbol, PERIOD_CURRENT, m_topSwingX + i);
         double low_i = iLow(_Symbol, PERIOD_CURRENT, m_topSwingX + i);

         if(i == 0 || high_i > boxTop)
            boxTop = high_i;
         if(i == 0 || low_i < boxBottom)
            boxBottom = low_i;
         if(i == 0)
            boxTime = iTime(_Symbol, PERIOD_CURRENT, m_topSwingX + i);
      }

      // Validate Order Block size
      double obSize = MathAbs(boxTop - boxBottom);
      if(obSize > atr * m_maxATRMult)
      {
         LogDebug("Bullish OB rejected - size too large: " + DoubleToString(obSize, _Digits));
         return false;
      }

      // Create Order Block
      CreateOB(boxTop, boxBottom, "Bull", boxTime);
      return true;
   }

   // Create bearish Order Block
   bool CreateBearishOB(double atr)
   {
      // Calculate Order Block boundaries (3-bar formation)
      double boxTop = 0, boxBottom = 0;
      datetime boxTime = 0;

      // Find the formation bars around the swing
      for(int i = 0; i < 3; i++)
      {
         double high_i = iHigh(_Symbol, PERIOD_CURRENT, m_btmSwingX + i);
         double low_i = iLow(_Symbol, PERIOD_CURRENT, m_btmSwingX + i);

         if(i == 0 || high_i > boxTop)
            boxTop = high_i;
         if(i == 0 || low_i < boxBottom)
            boxBottom = low_i;
         if(i == 0)
            boxTime = iTime(_Symbol, PERIOD_CURRENT, m_btmSwingX + i);
      }

      // Validate Order Block size
      double obSize = MathAbs(boxTop - boxBottom);
      if(obSize > atr * m_maxATRMult)
      {
         LogDebug("Bearish OB rejected - size too large: " + DoubleToString(obSize, _Digits));
         return false;
      }

      // Create Order Block
      CreateOB(boxTop, boxBottom, "Bear", boxTime);
      return true;
   }

   // Create new Order Block
   void CreateOB(double top, double bottom, string obType, datetime startTime)
   {
      // Resize array if needed
      int currentSize = ArraySize(m_activeOBs);
      if(currentSize >= m_maxOBs)
      {
         // Remove oldest OB
         for(int i = 0; i < currentSize - 1; i++)
         {
            m_activeOBs[i] = m_activeOBs[i + 1];
         }
         ArrayResize(m_activeOBs, currentSize - 1);
      }

      // Add new Order Block
      int newSize = ArraySize(m_activeOBs) + 1;
      ArrayResize(m_activeOBs, newSize);

      SOrderBlockInfo newOB = {};
      newOB.top = top;
      newOB.bottom = bottom;
      newOB.obType = obType;
      newOB.startTime = startTime;
      newOB.breaker = false;
      newOB.breakTime = 0;
      newOB.obVolume = CalculateOBVolume();

      m_activeOBs[newSize - 1] = newOB;
   }

   // Update active Order Blocks (check for invalidation)
   void UpdateActiveOBs()
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);
      double currentClose = iClose(_Symbol, PERIOD_CURRENT, 0);

      for(int i = 0; i < ArraySize(m_activeOBs); i++)
      {
         if(m_activeOBs[i].breaker)
            continue; // Already broken

         // Check Order Block invalidation
         if(m_activeOBs[i].obType == "Bull")
         {
            // Bullish OB: invalidated when price breaks below bottom
            if(currentLow < m_activeOBs[i].bottom)
            {
               m_activeOBs[i].breaker = true;
               m_activeOBs[i].breakTime = TimeCurrent();
               LogDebug("Bullish OB broken at " + DoubleToString(m_activeOBs[i].bottom, _Digits));
            }
         }
         else if(m_activeOBs[i].obType == "Bear")
         {
            // Bearish OB: invalidated when price breaks above top
            if(currentHigh > m_activeOBs[i].top)
            {
               m_activeOBs[i].breaker = true;
               m_activeOBs[i].breakTime = TimeCurrent();
               LogDebug("Bearish OB broken at " + DoubleToString(m_activeOBs[i].top, _Digits));
            }
         }
      }
   }

   // Calculate Order Block volume (3-bar sum)
   double CalculateOBVolume()
   {
      long vol0 = iVolume(_Symbol, PERIOD_CURRENT, 0);
      long vol1 = iVolume(_Symbol, PERIOD_CURRENT, 1);
      long vol2 = iVolume(_Symbol, PERIOD_CURRENT, 2);

      return (double)(vol0 + vol1 + vol2);
   }
};

//+------------------------------------------------------------------+
//| CRT State Machine Class                                         |
//+------------------------------------------------------------------+
class CCRTStateMachine
{
private:
   SCRTSetup         m_currentCRT;              // Current CRT setup
   bool              m_crtActive;               // CRT active flag
   bool              m_initialized;             // Initialization flag
   int               m_maxConcurrentCRTs;       // Maximum concurrent CRTs

public:
   // Constructor
   CCRTStateMachine(int maxConcurrentCRTs = 1)
   {
      m_maxConcurrentCRTs = maxConcurrentCRTs;
      m_crtActive = false;
      m_initialized = false;
      ResetCurrentCRT();
   }

   // Destructor
   ~CCRTStateMachine()
   {
      // No special cleanup needed
   }

   // Initialize state machine
   bool Initialize()
   {
      m_initialized = true;
      LogInfo("CRT State Machine initialized successfully");
      return true;
   }

   // Main state machine processing
   void ProcessStateMachine()
   {
      if(!m_initialized)
         return;

      // Check if we should create a new CRT
      if(!m_crtActive)
      {
         CreateNewCRT();
      }

      // Process current CRT state
      if(m_crtActive)
      {
         ProcessCurrentCRTState();
      }
   }

   // Get current CRT setup
   SCRTSetup GetCurrentCRT() const { return m_currentCRT; }

   // Check if CRT is active
   bool IsCRTActive() const { return m_crtActive; }

   // Check if state machine is ready
   bool IsReady() const { return m_initialized; }

   // Restore state from saved CRT setup (CRITICAL FIX)
   bool RestoreState(const SCRTSetup &savedCRT)
   {
      if(!m_initialized)
      {
         LogInfo("Cannot restore state - state machine not initialized");
         return false;
      }

      // Validate the saved state is restorable
      if(savedCRT.state == CRT_DONE || savedCRT.state == CRT_ABORTED)
      {
         LogInfo("Cannot restore terminal state - starting fresh");
         return false;
      }

      // Restore the CRT setup
      m_currentCRT = savedCRT;
      m_crtActive = true;

      LogInfo("State restored successfully - State: " + EnumToString(savedCRT.state) +
              ", Entry Type: " + savedCRT.entryType +
              ", Position Ticket: " + IntegerToString(savedCRT.positionTicket));

      return true;
   }

private:
   // Create new CRT setup
   void CreateNewCRT()
   {
      ResetCurrentCRT();
      m_currentCRT.state = CRT_WAITING_FOR_BULKY_CANDLE;
      m_currentCRT.startTime = TimeCurrent();
      m_crtActive = true;

      LogInfo("New CRT created - Waiting For Bulky Candle");
   }

   // Process current CRT state
   void ProcessCurrentCRTState()
   {
      switch(m_currentCRT.state)
      {
         case CRT_WAITING_FOR_BULKY_CANDLE:
            ProcessWaitingForBulkyCandle();
            break;

         case CRT_WAITING_FOR_SIDE_RETEST:
            ProcessWaitingForSideRetest();
            break;

         case CRT_WAITING_FOR_FVG:
            ProcessWaitingForFVG();
            break;

         case CRT_WAITING_FOR_OB:
            ProcessWaitingForOB();
            break;

         case CRT_WAITING_FOR_FVG_RETRACEMENT:
            ProcessWaitingForFVGRetracement();
            break;

         case CRT_WAITING_FOR_OB_RETRACEMENT:
            ProcessWaitingForOBRetracement();
            break;

         case CRT_ENTER_POSITION:
            ProcessEnterPosition();
            break;

         case CRT_ENTRY_TAKEN:
            ProcessEntryTaken();
            break;

         case CRT_ABORTED:
         case CRT_DONE:
            // Reset for new CRT
            m_crtActive = false;
            break;

         default:
            LogError("Unknown CRT state: " + IntegerToString(m_currentCRT.state));
            m_currentCRT.state = CRT_ABORTED;
            break;
      }
   }

   // State 1: Waiting For Bulky Candle
   void ProcessWaitingForBulkyCandle()
   {
      if(IsNewBulkyCandle())
      {
         m_currentCRT.bulkyHigh = GetLastBulkyHigh();
         m_currentCRT.bulkyLow = GetLastBulkyLow();
         m_currentCRT.bulkyTimeLow = TimeCurrent(); // Simplified for now
         m_currentCRT.bulkyTimeHigh = TimeCurrent(); // Simplified for now

         // Calculate setup quality score (Enhanced R:R)
         if(InpUseEnhancedRR)
         {
            m_currentCRT.qualityScore = CalculateQualityScore(m_currentCRT);
            m_currentCRT.dynamicRR = CalculateDynamicRR(m_currentCRT.qualityScore);
         }
         else
         {
            m_currentCRT.qualityScore = 0.0;
            m_currentCRT.dynamicRR = 0.39; // Default static R:R
         }

         m_currentCRT.state = CRT_WAITING_FOR_SIDE_RETEST;

         if(InpUseEnhancedRR)
         {
            LogInfo("Waiting For Side Retest | Quality Score: " + DoubleToString(m_currentCRT.qualityScore, 1) +
                    " | R:R: " + DoubleToString(m_currentCRT.dynamicRR, 2));
         }
         else
         {
            LogInfo("Waiting For Side Retest");
         }
      }
   }

   // State 2: Waiting For Side Retest
   void ProcessWaitingForSideRetest()
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);
      double currentClose = iClose(_Symbol, PERIOD_CURRENT, 0);

      // Check for abort conditions
      if(currentClose > m_currentCRT.bulkyHigh || currentClose < m_currentCRT.bulkyLow)
      {
         m_currentCRT.state = CRT_ABORTED;
         LogInfo("CRT Aborted - Price broke outside bulky candle range");
         return;
      }

      // Check for overlap conditions (exact Pine Script logic)
      bool bearOverlap = (currentHigh > m_currentCRT.bulkyHigh) && (currentClose <= m_currentCRT.bulkyHigh);
      bool bullOverlap = (currentLow < m_currentCRT.bulkyLow) && (currentClose >= m_currentCRT.bulkyLow);

      if(bearOverlap && !bullOverlap)
      {
         m_currentCRT.overlapDirection = OVERLAP_BEAR;
         m_currentCRT.breakTime = TimeCurrent();

         if(InpEntryMode == ENTRY_FVGS)
         {
            m_currentCRT.state = CRT_WAITING_FOR_FVG;
            LogInfo("Waiting For Bearish FVG");
         }
         else
         {
            m_currentCRT.state = CRT_WAITING_FOR_OB;
            LogInfo("Waiting For Bearish OB");
         }
      }
      else if(bullOverlap && !bearOverlap)
      {
         m_currentCRT.overlapDirection = OVERLAP_BULL;
         m_currentCRT.breakTime = TimeCurrent();

         if(InpEntryMode == ENTRY_FVGS)
         {
            m_currentCRT.state = CRT_WAITING_FOR_FVG;
            LogInfo("Waiting For Bullish FVG");
         }
         else
         {
            m_currentCRT.state = CRT_WAITING_FOR_OB;
            LogInfo("Waiting For Bullish OB");
         }
      }
   }

   // State 3a: Waiting For FVG
   void ProcessWaitingForFVG()
   {
      bool isBullish = (m_currentCRT.overlapDirection == OVERLAP_BULL);
      SFVGInfo latestFVG = GetLatestFVG(isBullish);

      if(IsFVGValid(latestFVG) && latestFVG.startTime > m_currentCRT.breakTime)
      {
         m_currentCRT.fvg = latestFVG;

         if(!InpRequireRetracement)
         {
            m_currentCRT.state = CRT_ENTER_POSITION;
            LogInfo("FVG found - Enter Position");
         }
         else
         {
            m_currentCRT.state = CRT_WAITING_FOR_FVG_RETRACEMENT;
            LogInfo("FVG found - Waiting For Retracement");
         }
      }
   }

   // State 3b: Waiting For OB
   void ProcessWaitingForOB()
   {
      bool isBullish = (m_currentCRT.overlapDirection == OVERLAP_BULL);
      SOrderBlockInfo latestOB = GetLatestOB(isBullish);

      if(IsOBValid(latestOB) && latestOB.startTime > m_currentCRT.breakTime)
      {
         m_currentCRT.ob = latestOB;

         if(!InpRequireRetracement)
         {
            m_currentCRT.state = CRT_ENTER_POSITION;
            LogInfo("Order Block found - Enter Position");
         }
         else
         {
            m_currentCRT.state = CRT_WAITING_FOR_OB_RETRACEMENT;
            LogInfo("Order Block found - Waiting For Retracement");
         }
      }
   }

   // State 4a: Waiting For FVG Retracement
   void ProcessWaitingForFVGRetracement()
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);

      // Check for retracement to FVG
      if(TimeCurrent() > m_currentCRT.fvg.startTime)
      {
         if(m_currentCRT.fvg.isBull && currentLow <= m_currentCRT.fvg.max)
         {
            m_currentCRT.state = CRT_ENTER_POSITION;
            LogInfo("FVG retracement detected - Enter Position");
         }
         else if(!m_currentCRT.fvg.isBull && currentHigh >= m_currentCRT.fvg.min)
         {
            m_currentCRT.state = CRT_ENTER_POSITION;
            LogInfo("FVG retracement detected - Enter Position");
         }
      }

      // Check for FVG invalidation
      if(m_currentCRT.fvg.isBull && currentLow < m_currentCRT.fvg.min)
      {
         m_currentCRT.fvgEndTime = TimeCurrent();
         m_currentCRT.state = CRT_ABORTED;
         LogInfo("CRT Aborted - FVG invalidated");
      }
      else if(!m_currentCRT.fvg.isBull && currentHigh > m_currentCRT.fvg.max)
      {
         m_currentCRT.fvgEndTime = TimeCurrent();
         m_currentCRT.state = CRT_ABORTED;
         LogInfo("CRT Aborted - FVG invalidated");
      }
   }

   // State 4b: Waiting For OB Retracement
   void ProcessWaitingForOBRetracement()
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);

      // Check for retracement to Order Block
      if(TimeCurrent() > m_currentCRT.ob.startTime)
      {
         if(m_currentCRT.ob.obType == "Bull" && currentLow <= m_currentCRT.ob.top)
         {
            m_currentCRT.state = CRT_ENTER_POSITION;
            LogInfo("OB retracement detected - Enter Position");
         }
         else if(m_currentCRT.ob.obType == "Bear" && currentHigh >= m_currentCRT.ob.bottom)
         {
            m_currentCRT.state = CRT_ENTER_POSITION;
            LogInfo("OB retracement detected - Enter Position");
         }
      }

      // Check for OB invalidation
      if(m_currentCRT.ob.obType == "Bull" && currentLow < m_currentCRT.ob.bottom)
      {
         m_currentCRT.ob.breakTime = TimeCurrent();
         m_currentCRT.state = CRT_ABORTED;
         LogInfo("CRT Aborted - Order Block invalidated");
      }
      else if(m_currentCRT.ob.obType == "Bear" && currentHigh > m_currentCRT.ob.top)
      {
         m_currentCRT.ob.breakTime = TimeCurrent();
         m_currentCRT.state = CRT_ABORTED;
         LogInfo("CRT Aborted - Order Block invalidated");
      }
   }

   // State 5: Enter Position (Enhanced with CEntryValidator)
   void ProcessEnterPosition()
   {
      // Use enhanced entry validator if available
      string rejectionReason = "";
      bool entryValid = false;

      if(g_entryValidator != NULL && g_entryValidator.IsReady())
      {
         // Enhanced validation with comprehensive checks
         entryValid = g_entryValidator.ValidateEntry(m_currentCRT, rejectionReason);
      }
      else
      {
         // Fallback to basic validation
         bool qualityValid = InpUseEnhancedRR ? ValidateEntryQuality() : true;
         bool volumeValid = IsVolumeValid();
         bool timeValid = IsValidTradingTime();

         entryValid = qualityValid && volumeValid && timeValid;

         if(!entryValid)
         {
            if(!qualityValid) rejectionReason += "Quality score too low";
            if(!volumeValid) rejectionReason += (rejectionReason != "" ? " | " : "") + "Volume too low";
            if(!timeValid) rejectionReason += (rejectionReason != "" ? " | " : "") + "Time filter active";
         }
      }

      if(entryValid)
      {
         // Entry validation passed - proceed to trade execution

         // Set entry type
         if(m_currentCRT.overlapDirection == OVERLAP_BULL)
         {
            m_currentCRT.entryType = "Long";
         }
         else
         {
            m_currentCRT.entryType = "Short";
         }

         // Set preliminary entry price for calculations
         m_currentCRT.entryPrice = (m_currentCRT.entryType == "Long") ?
                                   SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                                   SymbolInfoDouble(_Symbol, SYMBOL_BID);

         // Calculate TP/SL levels
         CalculateTPSLLevels();

         // REMOVED: Partial TP levels calculation (user request)

         // Calculate position sizing with risk management
         double positionSize = InpLotSize;

         // Calculate position size using risk manager if available
         if(g_riskManager != NULL && g_riskManager.IsReady())
         {
            double stopLossDistance = MathAbs(m_currentCRT.entryPrice - m_currentCRT.slTarget);
            positionSize = g_riskManager.CalculatePositionSize(m_currentCRT, stopLossDistance);
         }
         else if(g_entryValidator != NULL && g_entryValidator.IsReady())
         {
            positionSize = g_entryValidator.CalculatePositionSize(m_currentCRT);
         }

         // Validate risk limits
         string riskRejectionReason = "";
         bool riskValid = true;

         if(g_riskManager != NULL && g_riskManager.IsReady())
         {
            riskValid = g_riskManager.ValidateRiskLimits(m_currentCRT, positionSize, riskRejectionReason);
         }

         if(!riskValid)
         {
            LogInfo("Entry Rejected - Risk validation failed: " + riskRejectionReason);
            m_currentCRT.state = CRT_ABORTED;
            return;
         }

         // Execute actual trade
         string executionError = "";
         bool tradeExecuted = false;

         if(g_tradeExecutor != NULL && g_tradeExecutor.IsReady())
         {
            tradeExecuted = g_tradeExecutor.ExecuteMarketOrder(m_currentCRT, positionSize, executionError);

            if(tradeExecuted)
            {
               // Trade executed successfully
               m_currentCRT.entryTime = TimeCurrent();
               m_currentCRT.entryPrice = (m_currentCRT.entryType == "Long") ?
                                         SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                                         SymbolInfoDouble(_Symbol, SYMBOL_BID);
               m_currentCRT.positionTicket = g_tradeExecutor.GetCurrentTicket();
               m_currentCRT.positionSize = positionSize;
               m_currentCRT.state = CRT_ENTRY_TAKEN;

               // Enhanced logging with quality information
               if(InpUseEnhancedRR)
               {
                  LogInfo("Trade executed - " + m_currentCRT.entryType + " at " + DoubleToString(m_currentCRT.entryPrice, _Digits) +
                          " | Ticket: " + IntegerToString(m_currentCRT.positionTicket) +
                          " | Quality Score: " + DoubleToString(m_currentCRT.qualityScore, 1) +
                          " | R:R: " + DoubleToString(m_currentCRT.dynamicRR, 2) +
                          " | Position Size: " + DoubleToString(positionSize, 2));
               }
               else
               {
                  LogInfo("Trade executed - " + m_currentCRT.entryType + " at " + DoubleToString(m_currentCRT.entryPrice, _Digits) +
                          " | Ticket: " + IntegerToString(m_currentCRT.positionTicket) +
                          " | Position Size: " + DoubleToString(positionSize, 2));
               }
            }
            else
            {
               // Trade execution failed
               LogInfo("Trade execution failed: " + executionError);
               m_currentCRT.state = CRT_ABORTED;
               return;
            }
         }
         else
         {
            // Fallback to simulation mode (for testing)
            LogInfo("Trade executor not available - Running in simulation mode");
            m_currentCRT.entryTime = TimeCurrent();
            m_currentCRT.entryPrice = (m_currentCRT.entryType == "Long") ?
                                      SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                                      SymbolInfoDouble(_Symbol, SYMBOL_BID);
            m_currentCRT.positionTicket = 0; // Simulation mode
            m_currentCRT.positionSize = positionSize;
            m_currentCRT.state = CRT_ENTRY_TAKEN;

            LogInfo("Simulated entry - " + m_currentCRT.entryType + " at " + DoubleToString(m_currentCRT.entryPrice, _Digits) +
                    " | Position Size: " + DoubleToString(positionSize, 2));
         }
      }
      else
      {
         // Entry validation failed
         LogInfo("Entry Rejected - " + rejectionReason);
         m_currentCRT.state = CRT_ABORTED;
      }
   }

   // State 6: Entry Taken (Enhanced with Real Position Management)
   void ProcessEntryTaken()
   {
      // Check if position still exists (for real trading)
      if(m_currentCRT.positionTicket > 0)
      {
         if(g_tradeExecutor != NULL && g_tradeExecutor.IsReady())
         {
            if(!g_tradeExecutor.PositionExists(m_currentCRT.positionTicket))
            {
               // Position was closed externally or by broker
               LogInfo("Position closed externally - Ticket: " + IntegerToString(m_currentCRT.positionTicket));
               m_currentCRT.exitTime = TimeCurrent();
               m_currentCRT.state = CRT_DONE;
               return;
            }

            // Update position info
            double currentVolume, openPrice, currentSL, currentTP;
            if(g_tradeExecutor.GetPositionInfo(m_currentCRT.positionTicket, currentVolume, openPrice, currentSL, currentTP))
            {
               // Update position size if it changed (partial closures)
               if(currentVolume != m_currentCRT.positionSize)
               {
                  LogInfo("Position size changed - Old: " + DoubleToString(m_currentCRT.positionSize, 2) +
                          ", New: " + DoubleToString(currentVolume, 2));
                  m_currentCRT.positionSize = currentVolume;
               }
            }
         }
      }

      // REMOVED: Partial TP processing (user request)

      // Check for simple TP/SL hits
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);

      bool tradeCompleted = false;

      if(m_currentCRT.entryType == "Long")
      {
         // Check for TP hit
         if(currentHigh >= m_currentCRT.tpTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.tpTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_TAKE_PROFIT;
            m_currentCRT.tradeResult = "Win";
            tradeCompleted = true;
         }
         // Check for SL hit
         else if(currentLow <= m_currentCRT.slTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.slTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_STOP_LOSS;
            m_currentCRT.tradeResult = "Loss";
            tradeCompleted = true;
         }
      }
      else if(m_currentCRT.entryType == "Short")
      {
         // Check for TP hit
         if(currentLow <= m_currentCRT.tpTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.tpTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_TAKE_PROFIT;
            m_currentCRT.tradeResult = "Win";
            tradeCompleted = true;
         }
         // Check for SL hit
         else if(currentHigh >= m_currentCRT.slTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.slTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_STOP_LOSS;
            m_currentCRT.tradeResult = "Loss";
            tradeCompleted = true;
         }
      }

      if(tradeCompleted)
      {
         // Close position if still open
         if(m_currentCRT.positionTicket > 0 && g_tradeExecutor != NULL && g_tradeExecutor.IsReady())
         {
            string closeError = "";
            if(g_tradeExecutor.PositionExists(m_currentCRT.positionTicket))
            {
               if(!g_tradeExecutor.ClosePosition(m_currentCRT.positionTicket, m_currentCRT.positionSize, closeError))
               {
                  LogInfo("Failed to close position: " + closeError);
               }
            }
         }

         // Calculate actual R:R
         if(m_currentCRT.exitPrice != 0 && m_currentCRT.entryPrice != 0)
         {
            m_currentCRT.actualRR = CalculateRRRatio(m_currentCRT);
         }

         // REMOVED: Performance monitor recording (Phase 9 removed per user request)

         LogInfo("Trade completed - " + m_currentCRT.tradeResult +
                 " | Entry: " + DoubleToString(m_currentCRT.entryPrice, _Digits) +
                 " | Exit: " + DoubleToString(m_currentCRT.exitPrice, _Digits) +
                 " | R:R: " + DoubleToString(m_currentCRT.actualRR, 2));

         m_currentCRT.state = CRT_DONE;
         return;
      }

      // REMOVED: Partial TP debugging (user request)

      // Basic trade management without partial TPs (reuse variables from above)

      // Check for basic TP/SL hits
      if(m_currentCRT.entryType == "Long")
      {
         if(currentHigh >= m_currentCRT.tpTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.tpTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_TAKE_PROFIT;
            m_currentCRT.tradeResult = "Win";
            LogInfo("TP hit at " + DoubleToString(m_currentCRT.tpTarget, _Digits));
         }
         else if(currentLow <= m_currentCRT.slTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.slTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_STOP_LOSS;
            m_currentCRT.tradeResult = "Loss";
            LogInfo("SL hit at " + DoubleToString(m_currentCRT.slTarget, _Digits));
         }
      }
      else if(m_currentCRT.entryType == "Short")
      {
         if(currentLow <= m_currentCRT.tpTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.tpTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_TAKE_PROFIT;
            m_currentCRT.tradeResult = "Win";
            LogInfo("TP hit at " + DoubleToString(m_currentCRT.tpTarget, _Digits));
         }
         else if(currentHigh >= m_currentCRT.slTarget)
         {
            m_currentCRT.exitPrice = m_currentCRT.slTarget;
            m_currentCRT.exitTime = TimeCurrent();
            m_currentCRT.state = CRT_STOP_LOSS;
            m_currentCRT.tradeResult = "Loss";
            LogInfo("SL hit at " + DoubleToString(m_currentCRT.slTarget, _Digits));
         }
      }

      // Mark as done if trade completed
      if(m_currentCRT.state == CRT_TAKE_PROFIT || m_currentCRT.state == CRT_STOP_LOSS)
      {
         m_currentCRT.actualRR = CalculateRRRatio(m_currentCRT);
         LogInfo("Trade completed (basic) - " + m_currentCRT.tradeResult +
                 " | R:R: " + DoubleToString(m_currentCRT.actualRR, 2));
         m_currentCRT.state = CRT_DONE;
      }
   }

   // Reset current CRT setup
   void ResetCurrentCRT()
   {
      SCRTSetup emptyCRT = {};
      m_currentCRT = emptyCRT;
   }

   // Validate entry quality (Enhanced R:R)
   bool ValidateEntryQuality()
   {
      return (m_currentCRT.qualityScore >= InpMinQualityScore);
   }

   // Calculate TP/SL levels (Enhanced with Risk Manager)
   void CalculateTPSLLevels()
   {
      // Use enhanced TP/SL calculation if risk manager is available
      if(g_riskManager != NULL && g_riskManager.IsReady())
      {
         if(g_riskManager.CalculateTPSLLevels(m_currentCRT))
         {
            return; // Successfully calculated with risk manager
         }
      }

      // Fallback to basic TP/SL calculation
      double currentATR = GetCurrentATR();

      if(InpTPSLMethod == TPSL_FIXED)
      {
         // Fixed percentage TP/SL
         if(m_currentCRT.entryType == "Long")
         {
            m_currentCRT.slTarget = m_currentCRT.entryPrice * (1.0 - InpSLPercent / 100.0);
            m_currentCRT.tpTarget = m_currentCRT.entryPrice * (1.0 + InpTPPercent / 100.0);
         }
         else
         {
            m_currentCRT.slTarget = m_currentCRT.entryPrice * (1.0 + InpSLPercent / 100.0);
            m_currentCRT.tpTarget = m_currentCRT.entryPrice * (1.0 - InpTPPercent / 100.0);
         }
      }
      else
      {
         // Dynamic ATR-based TP/SL
         if(m_currentCRT.entryType == "Long")
         {
            m_currentCRT.slTarget = m_currentCRT.entryPrice - (currentATR * g_slATRMult);
            double riskDistance = MathAbs(m_currentCRT.entryPrice - m_currentCRT.slTarget);
            double enhancedRR = InpUseEnhancedRR ? m_currentCRT.dynamicRR : 0.39;
            m_currentCRT.tpTarget = m_currentCRT.entryPrice + (riskDistance * enhancedRR);
         }
         else
         {
            m_currentCRT.slTarget = m_currentCRT.entryPrice + (currentATR * g_slATRMult);
            double riskDistance = MathAbs(m_currentCRT.entryPrice - m_currentCRT.slTarget);
            double enhancedRR = InpUseEnhancedRR ? m_currentCRT.dynamicRR : 0.39;
            m_currentCRT.tpTarget = m_currentCRT.entryPrice - (riskDistance * enhancedRR);
         }
      }

      LogInfo("TP/SL calculated (fallback) - TP: " + DoubleToString(m_currentCRT.tpTarget, _Digits) +
              ", SL: " + DoubleToString(m_currentCRT.slTarget, _Digits));
   }
};

//+------------------------------------------------------------------+
//| Enhanced Entry Validator Class                                  |
//+------------------------------------------------------------------+
class CEntryValidator
{
private:
   bool              m_initialized;              // Initialization flag
   double            m_maxSpreadPoints;          // Maximum spread in points
   double            m_maxSlippagePoints;        // Maximum slippage in points
   double            m_minLiquidity;             // Minimum liquidity requirement
   double            m_maxAccountRiskPercent;    // Maximum account risk per trade
   double            m_maxDailyLossPercent;      // Maximum daily loss limit
   double            m_maxDrawdownPercent;       // Maximum drawdown limit
   bool              m_newsFilterEnabled;        // News filter enabled flag

public:
   // Constructor
   CEntryValidator()
   {
      m_initialized = false;
      m_maxSpreadPoints = 30.0;        // 3 pips for major pairs
      m_maxSlippagePoints = 20.0;      // 2 pips maximum slippage
      m_minLiquidity = 1000000.0;      // Minimum 1M volume
      m_maxAccountRiskPercent = 5.0;   // Maximum 5% account risk
      m_maxDailyLossPercent = 10.0;    // Maximum 10% daily loss
      m_maxDrawdownPercent = 20.0;     // Maximum 20% drawdown
      m_newsFilterEnabled = false;     // Disabled by default
   }

   // Destructor
   ~CEntryValidator()
   {
      // No special cleanup needed
   }

   // Initialize entry validator
   bool Initialize()
   {
      // Set parameters based on input settings
      m_maxAccountRiskPercent = InpMaxRiskPercent;

      m_initialized = true;
      LogInfo("Entry Validator initialized successfully");
      return true;
   }

   // Comprehensive entry validation
   bool ValidateEntry(const SCRTSetup &crtSetup, string &rejectionReason)
   {
      if(!m_initialized)
      {
         rejectionReason = "Entry validator not initialized";
         return false;
      }

      // 1. Enhanced Quality Validation
      if(!ValidateQuality(crtSetup, rejectionReason))
         return false;

      // 2. Market Condition Validation
      if(!ValidateMarketConditions(rejectionReason))
         return false;

      // 3. Risk Validation
      if(!ValidateRisk(crtSetup, rejectionReason))
         return false;

      // 4. Volume Confirmation
      if(!ValidateVolume(rejectionReason))
         return false;

      // 5. Time Filtering
      if(!ValidateTime(rejectionReason))
         return false;

      // All validations passed
      return true;
   }

   // Calculate position sizing (simplified - removed partial TP logic)
   double CalculatePositionSize(const SCRTSetup &crtSetup)
   {
      if(!m_initialized)
         return InpLotSize;

      // Calculate risk-based position size
      if(!InpUseFixedLots)
      {
         double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * (m_maxAccountRiskPercent / 100.0);
         double stopLossDistance = MathAbs(crtSetup.entryPrice - crtSetup.slTarget);
         return CalculateLotSize(riskAmount, stopLossDistance);
      }

      return InpLotSize;
   }

   // Calculate enhanced quality score with confluence factors
   double CalculateEnhancedQualityScore(const SCRTSetup &crtSetup)
   {
      double score = 0.0;

      // 1. Bulky candle range quality (base score)
      double currentATR = GetCurrentATR();
      if(currentATR > 0)
      {
         double bulkyRange = MathAbs(crtSetup.bulkyHigh - crtSetup.bulkyLow);
         double rangeRatio = bulkyRange / currentATR;

         if(rangeRatio >= 2.5)      score += 2.0;  // Excellent range
         else if(rangeRatio >= 2.0) score += 1.5;  // Good range
         else if(rangeRatio >= 1.5) score += 1.0;  // Average range
         else                       score += 0.5;  // Poor range
      }

      // 2. Volume confirmation quality
      if(IsVolumeValid())
         score += 1.0;

      // 3. Time-based quality
      if(IsValidTradingTime())
         score += 0.5;

      // 4. FVG/OB strength (confluence factor)
      if(InpEntryMode == ENTRY_FVGS && IsFVGValid(crtSetup.fvg))
      {
         double fvgSize = MathAbs(crtSetup.fvg.max - crtSetup.fvg.min);
         if(fvgSize > currentATR * 0.5)
            score += 0.5;  // Strong FVG
      }
      else if(InpEntryMode == ENTRY_ORDER_BLOCKS && IsOBValid(crtSetup.ob))
      {
         double obSize = MathAbs(crtSetup.ob.top - crtSetup.ob.bottom);
         if(obSize > currentATR * 0.3)
            score += 0.5;  // Strong Order Block
      }

      // 5. Market structure alignment (placeholder for future enhancement)
      // - Trend alignment
      // - Support/resistance confluence
      // - Multiple timeframe confirmation

      return MathMin(5.0, MathMax(0.0, score)); // Clamp between 0-5
   }

   // REMOVED: CalculatePartialLevels method (user request)

   // Check if entry validator is ready
   bool IsReady() const { return m_initialized; }

private:
   // Validate quality score and enhanced R:R requirements
   bool ValidateQuality(const SCRTSetup &crtSetup, string &rejectionReason)
   {
      if(!InpUseEnhancedRR)
         return true; // Skip quality validation if enhanced R:R is disabled

      if(crtSetup.qualityScore < InpMinQualityScore)
      {
         rejectionReason = "Quality Score: " + DoubleToString(crtSetup.qualityScore, 1) +
                          " below minimum: " + DoubleToString(InpMinQualityScore, 1);
         return false;
      }

      return true;
   }

   // Validate market conditions (spread, liquidity, market hours)
   bool ValidateMarketConditions(string &rejectionReason)
   {
      // 1. Spread validation
      long currentSpreadLong = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
      double currentSpread = (double)currentSpreadLong;
      if(currentSpread > m_maxSpreadPoints)
      {
         rejectionReason = "Spread too high: " + DoubleToString(currentSpread, 1) +
                          " > " + DoubleToString(m_maxSpreadPoints, 1);
         return false;
      }

      // 2. Market hours validation
      if(!IsMarketOpen())
      {
         rejectionReason = "Market closed";
         return false;
      }

      // 3. Liquidity validation (simplified)
      long currentVolume = iVolume(_Symbol, PERIOD_CURRENT, 0);
      if(currentVolume < m_minLiquidity)
      {
         rejectionReason = "Insufficient liquidity: " + IntegerToString(currentVolume);
         return false;
      }

      // 4. News filter (placeholder)
      if(m_newsFilterEnabled && IsHighImpactNews())
      {
         rejectionReason = "High impact news detected";
         return false;
      }

      return true;
   }

   // Validate risk parameters
   bool ValidateRisk(const SCRTSetup &crtSetup, string &rejectionReason)
   {
      // 1. Account risk validation
      double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      double riskAmount = MathAbs(crtSetup.entryPrice - crtSetup.slTarget) * InpLotSize *
                         SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE) /
                         SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
      double riskPercent = (riskAmount / accountBalance) * 100.0;

      if(riskPercent > m_maxAccountRiskPercent)
      {
         rejectionReason = "Risk too high: " + DoubleToString(riskPercent, 2) +
                          "% > " + DoubleToString(m_maxAccountRiskPercent, 2) + "%";
         return false;
      }

      // 2. Maximum concurrent trades
      int openPositions = PositionsTotal();
      if(openPositions >= InpMaxConcurrentTrades)
      {
         rejectionReason = "Maximum concurrent trades reached: " + IntegerToString(openPositions);
         return false;
      }

      // 3. Daily loss limit (placeholder)
      // 4. Drawdown protection (placeholder)

      return true;
   }

   // Validate volume confirmation
   bool ValidateVolume(string &rejectionReason)
   {
      if(!IsVolumeValid())
      {
         rejectionReason = "Volume too low";
         return false;
      }

      return true;
   }

   // Validate time filtering
   bool ValidateTime(string &rejectionReason)
   {
      if(!IsValidTradingTime())
      {
         rejectionReason = "Time filter active";
         return false;
      }

      return true;
   }

   // REMOVED: GetIntelligentSizing method (user request)

   // Check if market is open
   bool IsMarketOpen()
   {
      // Simplified market hours check
      MqlDateTime timeStruct;
      TimeToStruct(TimeCurrent(), timeStruct);
      int currentHour = timeStruct.hour;
      int currentDayOfWeek = timeStruct.day_of_week;

      // Weekend check
      if(currentDayOfWeek == 0 || currentDayOfWeek == 6) // Sunday = 0, Saturday = 6
         return false;

      // Basic trading hours (can be enhanced)
      return (currentHour >= 1 && currentHour <= 23); // 01:00 - 23:00
   }

   // Check for high impact news (placeholder)
   bool IsHighImpactNews()
   {
      // Placeholder for news filter implementation
      // This would integrate with economic calendar API
      return false;
   }
};

//+------------------------------------------------------------------+
//| Risk Manager Class                                              |
//+------------------------------------------------------------------+
class CRiskManager
{
private:
   bool              m_initialized;              // Initialization flag
   double            m_maxRiskPerTrade;          // Maximum risk per trade (%)
   double            m_maxDailyRisk;             // Maximum daily risk (%)
   double            m_maxDrawdown;              // Maximum drawdown (%)
   double            m_accountBalance;           // Current account balance
   double            m_accountEquity;            // Current account equity
   double            m_dailyPnL;                 // Daily P&L tracking
   int               m_maxPositions;             // Maximum concurrent positions
   double            m_correlationThreshold;     // Position correlation threshold

   // Performance tracking
   double            m_totalRR;                  // Total R:R achieved
   int               m_totalTrades;              // Total trades executed
   int               m_winningTrades;            // Winning trades count
   int               m_losingTrades;             // Losing trades count

public:
   // Constructor
   CRiskManager()
   {
      m_initialized = false;
      m_maxRiskPerTrade = 2.0;      // 2% default
      m_maxDailyRisk = 6.0;         // 6% daily limit
      m_maxDrawdown = 20.0;         // 20% drawdown limit
      m_accountBalance = 0.0;
      m_accountEquity = 0.0;
      m_dailyPnL = 0.0;
      m_maxPositions = 3;
      m_correlationThreshold = 0.7;
      m_totalRR = 0.0;
      m_totalTrades = 0;
      m_winningTrades = 0;
      m_losingTrades = 0;
   }

   // Destructor
   ~CRiskManager()
   {
      // No special cleanup needed
   }

   // Initialize risk manager
   bool Initialize()
   {
      // Set parameters from input settings
      m_maxRiskPerTrade = InpMaxRiskPercent;
      m_maxPositions = InpMaxConcurrentTrades;

      // Update account information
      UpdateAccountInfo();

      m_initialized = true;
      LogInfo("Risk Manager initialized successfully");
      return true;
   }

   // Calculate optimal position size
   double CalculatePositionSize(const SCRTSetup &crtSetup, double stopLossDistance)
   {
      if(!m_initialized || stopLossDistance <= 0)
         return InpLotSize;

      // Update account information
      UpdateAccountInfo();

      // Method 1: Fixed lot size
      if(InpUseFixedLots)
         return ValidatePositionSize(InpLotSize);

      // Method 2: Risk-based position sizing
      double riskAmount = m_accountBalance * (m_maxRiskPerTrade / 100.0);
      double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
      double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

      if(tickValue <= 0 || tickSize <= 0)
         return ValidatePositionSize(InpLotSize);

      // Calculate position size based on risk
      double positionSize = riskAmount / (stopLossDistance * tickValue / tickSize);

      // Apply quality-based adjustment (Enhanced R:R)
      if(InpUseEnhancedRR && crtSetup.qualityScore > 0)
      {
         double qualityMultiplier = CalculateQualityMultiplier(crtSetup.qualityScore);
         positionSize *= qualityMultiplier;
      }

      return ValidatePositionSize(positionSize);
   }

   // Calculate enhanced TP/SL levels
   bool CalculateTPSLLevels(SCRTSetup &crtSetup)
   {
      if(!m_initialized)
         return false;

      double currentATR = GetCurrentATR();
      if(currentATR <= 0)
         return false;

      if(InpTPSLMethod == TPSL_FIXED)
      {
         // Fixed percentage TP/SL (exact Pine Script implementation)
         if(crtSetup.entryType == "Long")
         {
            crtSetup.slTarget = crtSetup.entryPrice * (1.0 - InpSLPercent / 100.0);
            crtSetup.tpTarget = crtSetup.entryPrice * (1.0 + InpTPPercent / 100.0);
         }
         else
         {
            crtSetup.slTarget = crtSetup.entryPrice * (1.0 + InpSLPercent / 100.0);
            crtSetup.tpTarget = crtSetup.entryPrice * (1.0 - InpTPPercent / 100.0);
         }
      }
      else
      {
         // Dynamic ATR-based TP/SL (exact Pine Script implementation)
         if(crtSetup.entryType == "Long")
         {
            crtSetup.slTarget = crtSetup.entryPrice - (currentATR * g_slATRMult);
            double riskDistance = MathAbs(crtSetup.entryPrice - crtSetup.slTarget);
            double enhancedRR = InpUseEnhancedRR ? crtSetup.dynamicRR : 0.39;
            crtSetup.tpTarget = crtSetup.entryPrice + (riskDistance * enhancedRR);
         }
         else
         {
            crtSetup.slTarget = crtSetup.entryPrice + (currentATR * g_slATRMult);
            double riskDistance = MathAbs(crtSetup.entryPrice - crtSetup.slTarget);
            double enhancedRR = InpUseEnhancedRR ? crtSetup.dynamicRR : 0.39;
            crtSetup.tpTarget = crtSetup.entryPrice - (riskDistance * enhancedRR);
         }
      }

      LogInfo("TP/SL calculated - Entry: " + DoubleToString(crtSetup.entryPrice, _Digits) +
              ", TP: " + DoubleToString(crtSetup.tpTarget, _Digits) +
              ", SL: " + DoubleToString(crtSetup.slTarget, _Digits));

      return true;
   }

   // REMOVED: UpdateTrailingStop method (user request)

   // Calculate R:R ratio (exact Pine Script implementation)
   double CalculateRR(const SCRTSetup &crtSetup)
   {
      if(crtSetup.entryPrice == 0 || crtSetup.slTarget == 0 || crtSetup.exitPrice == 0)
         return 0.0;

      // Calculate risk (distance to stop loss)
      double risk = MathAbs(crtSetup.entryPrice - crtSetup.slTarget);

      // Calculate actual reward/loss (distance to exit)
      double reward = crtSetup.exitPrice - crtSetup.entryPrice;

      // For short trades, invert the reward calculation
      if(crtSetup.entryType == "Short")
         reward = crtSetup.entryPrice - crtSetup.exitPrice;

      // Return R:R ratio (positive for profit, negative for loss)
      return (risk > 0) ? reward / risk : 0.0;
   }

   // Validate risk limits
   bool ValidateRiskLimits(const SCRTSetup &crtSetup, double positionSize, string &rejectionReason)
   {
      if(!m_initialized)
      {
         rejectionReason = "Risk manager not initialized";
         return false;
      }

      // Update account information
      UpdateAccountInfo();

      // 1. Check maximum risk per trade
      double stopLossDistance = MathAbs(crtSetup.entryPrice - crtSetup.slTarget);
      double tradeRisk = CalculateTradeRisk(positionSize, stopLossDistance);
      double riskPercent = (tradeRisk / m_accountBalance) * 100.0;

      if(riskPercent > m_maxRiskPerTrade)
      {
         rejectionReason = "Trade risk too high: " + DoubleToString(riskPercent, 2) +
                          "% > " + DoubleToString(m_maxRiskPerTrade, 2) + "%";
         return false;
      }

      // 2. Check maximum concurrent positions
      int currentPositions = PositionsTotal();
      if(currentPositions >= m_maxPositions)
      {
         rejectionReason = "Maximum positions reached: " + IntegerToString(currentPositions);
         return false;
      }

      // 3. Check daily risk limit
      double projectedDailyRisk = MathAbs(m_dailyPnL) + tradeRisk;
      double dailyRiskPercent = (projectedDailyRisk / m_accountBalance) * 100.0;

      if(dailyRiskPercent > m_maxDailyRisk)
      {
         rejectionReason = "Daily risk limit exceeded: " + DoubleToString(dailyRiskPercent, 2) +
                          "% > " + DoubleToString(m_maxDailyRisk, 2) + "%";
         return false;
      }

      // 4. Check drawdown limit
      double currentDrawdown = ((m_accountBalance - m_accountEquity) / m_accountBalance) * 100.0;
      if(currentDrawdown > m_maxDrawdown)
      {
         rejectionReason = "Drawdown limit exceeded: " + DoubleToString(currentDrawdown, 2) +
                          "% > " + DoubleToString(m_maxDrawdown, 2) + "%";
         return false;
      }

      return true;
   }

   // Update performance statistics
   void UpdatePerformanceStats(const SCRTSetup &crtSetup)
   {
      if(crtSetup.exitPrice == 0 || crtSetup.entryPrice == 0)
         return;

      m_totalTrades++;

      // Calculate R:R for this trade
      double tradeRR = CalculateRR(crtSetup);
      m_totalRR += tradeRR;

      // Determine if trade was successful
      bool isWinning = false;
      if(crtSetup.entryType == "Long")
         isWinning = (crtSetup.exitPrice > crtSetup.entryPrice);
      else
         isWinning = (crtSetup.exitPrice < crtSetup.entryPrice);

      if(isWinning)
         m_winningTrades++;
      else
         m_losingTrades++;

      LogInfo("Trade completed - R:R: " + DoubleToString(tradeRR, 2) +
              ", Result: " + (isWinning ? "Win" : "Loss") +
              ", Win Rate: " + DoubleToString(GetWinRate(), 1) + "%");
   }

   // Get performance statistics
   double GetWinRate() const
   {
      return (m_totalTrades > 0) ? (double)m_winningTrades / m_totalTrades * 100.0 : 0.0;
   }

   double GetAverageRR() const
   {
      return (m_totalTrades > 0) ? m_totalRR / m_totalTrades : 0.0;
   }

   // Check if risk manager is ready
   bool IsReady() const { return m_initialized; }

private:
   // Update account information
   void UpdateAccountInfo()
   {
      m_accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      m_accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);

      // Update daily P&L (simplified - would need more sophisticated tracking)
      static double lastEquity = m_accountEquity;
      if(lastEquity > 0)
         m_dailyPnL = m_accountEquity - lastEquity;
      lastEquity = m_accountEquity;
   }

   // Validate position size against broker limits
   double ValidatePositionSize(double positionSize)
   {
      double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
      double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

      // Clamp to broker limits
      positionSize = MathMax(minLot, MathMin(maxLot, positionSize));

      // Round to lot step
      positionSize = MathFloor(positionSize / lotStep) * lotStep;

      return positionSize;
   }

   // Calculate quality multiplier for position sizing
   double CalculateQualityMultiplier(double qualityScore)
   {
      // High quality setups get larger position sizes
      if(qualityScore >= 4.0)
         return 1.5;  // 50% larger
      else if(qualityScore >= 3.0)
         return 1.25; // 25% larger
      else if(qualityScore >= 2.0)
         return 1.0;  // Normal size
      else if(qualityScore >= 1.0)
         return 0.75; // 25% smaller
      else
         return 0.5;  // 50% smaller
   }

   // Calculate trade risk in account currency
   double CalculateTradeRisk(double positionSize, double stopLossDistance)
   {
      double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
      double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

      if(tickValue <= 0 || tickSize <= 0)
         return 0.0;

      return positionSize * stopLossDistance * tickValue / tickSize;
   }
};

// REMOVED: CPartialTPManager class (user request)

// REMOVED: CPartialTPManager methods (user request)

// REMOVED: All CPartialTPManager methods (user request)

//+------------------------------------------------------------------+
//| Trade Executor Class                                            |
//+------------------------------------------------------------------+
class CTradeExecutor
{
private:
   bool              m_initialized;              // Initialization flag
   ulong             m_magicNumber;              // Magic number for orders
   int               m_slippage;                 // Slippage in points
   int               m_maxRetries;               // Maximum retry attempts
   int               m_retryDelay;               // Delay between retries (ms)

   // Position tracking
   ulong             m_currentTicket;            // Current position ticket
   double            m_currentVolume;            // Current position volume
   string            m_currentSymbol;            // Current position symbol

public:
   // Constructor
   CTradeExecutor()
   {
      m_initialized = false;
      m_magicNumber = 0;
      m_slippage = 10;
      m_maxRetries = 3;
      m_retryDelay = 1000;
      m_currentTicket = 0;
      m_currentVolume = 0.0;
      m_currentSymbol = "";
   }

   // Destructor
   ~CTradeExecutor()
   {
      // No special cleanup needed
   }

   // Initialize trade executor
   bool Initialize()
   {
      // Set parameters from input settings
      m_magicNumber = InpMagicNumber;
      m_slippage = 10; // 1 pip slippage

      m_initialized = true;
      LogInfo("Trade Executor initialized successfully");
      return true;
   }

   // Execute market order (main entry point)
   bool ExecuteMarketOrder(const SCRTSetup &crtSetup, double volume, string &errorMessage)
   {
      if(!m_initialized)
      {
         errorMessage = "Trade executor not initialized";
         return false;
      }

      // Validate order parameters
      if(!ValidateOrderParameters(crtSetup, volume, errorMessage))
         return false;

      // Prepare trade request
      MqlTradeRequest request = {};
      MqlTradeResult result = {};

      if(!PrepareTradeRequest(crtSetup, volume, request, errorMessage))
         return false;

      // ADDED: Validate request with OrderCheck() before sending
      MqlTradeCheckResult checkResult = {};
      if(!OrderCheck(request, checkResult))
      {
         errorMessage = "OrderCheck failed - Code: " + IntegerToString(checkResult.retcode) +
                       ", Comment: " + checkResult.comment;
         LogInfo("Order validation failed: " + errorMessage);
         return false;
      }

      // Execute order with retry logic
      bool success = false;
      for(int attempt = 1; attempt <= m_maxRetries; attempt++)
      {
         // Clear previous result
         ZeroMemory(result);

         // Send order
         if(OrderSend(request, result))
         {
            // Check result code (FIXED: Added TRADE_RETCODE_PLACED)
            if(result.retcode == TRADE_RETCODE_DONE || result.retcode == TRADE_RETCODE_PLACED)
            {
               success = true;
               // CRITICAL FIX: Use result.deal for market orders, result.order for pending orders
               m_currentTicket = (result.retcode == TRADE_RETCODE_DONE) ? result.deal : result.order;
               m_currentVolume = volume;
               m_currentSymbol = _Symbol;

               LogInfo("Order executed successfully - Ticket: " + IntegerToString(m_currentTicket) +
                       ", Volume: " + DoubleToString(volume, 2) +
                       ", Price: " + DoubleToString(result.price, _Digits) +
                       ", Retcode: " + IntegerToString(result.retcode));
               break;
            }
            else
            {
               errorMessage = "Order failed - Code: " + IntegerToString(result.retcode) +
                             ", Comment: " + result.comment;

               // Check if retry is appropriate
               if(!ShouldRetry(result.retcode))
                  break;

               if(attempt < m_maxRetries)
               {
                  LogInfo("Order attempt " + IntegerToString(attempt) + " failed, retrying... " + errorMessage);
                  Sleep(m_retryDelay);
               }
            }
         }
         else
         {
            errorMessage = "OrderSend failed - Error: " + IntegerToString(GetLastError());
            LogInfo("OrderSend attempt " + IntegerToString(attempt) + " failed: " + errorMessage);

            if(attempt < m_maxRetries)
               Sleep(m_retryDelay);
         }
      }

      if(!success)
      {
         LogInfo("Order execution failed after " + IntegerToString(m_maxRetries) + " attempts: " + errorMessage);
      }

      return success;
   }

   // Modify position TP/SL
   bool ModifyPosition(ulong ticket, double newSL, double newTP, string &errorMessage)
   {
      if(!PositionSelectByTicket(ticket))
      {
         errorMessage = "Position not found: " + IntegerToString(ticket);
         return false;
      }

      // Prepare modification request
      MqlTradeRequest request = {};
      MqlTradeResult result = {};

      request.action = TRADE_ACTION_SLTP;
      request.position = ticket;
      request.symbol = PositionGetString(POSITION_SYMBOL);
      request.sl = newSL;
      request.tp = newTP;
      request.magic = m_magicNumber;

      // Execute modification with retry logic
      bool success = false;
      for(int attempt = 1; attempt <= m_maxRetries; attempt++)
      {
         ZeroMemory(result);

         if(OrderSend(request, result))
         {
            if(result.retcode == TRADE_RETCODE_DONE)
            {
               success = true;
               LogInfo("Position modified successfully - Ticket: " + IntegerToString(ticket) +
                       ", SL: " + DoubleToString(newSL, _Digits) +
                       ", TP: " + DoubleToString(newTP, _Digits));
               break;
            }
            else
            {
               errorMessage = "Modification failed - Code: " + IntegerToString(result.retcode) +
                             ", Comment: " + result.comment;

               if(!ShouldRetry(result.retcode))
                  break;

               if(attempt < m_maxRetries)
               {
                  LogInfo("Modification attempt " + IntegerToString(attempt) + " failed, retrying...");
                  Sleep(m_retryDelay);
               }
            }
         }
         else
         {
            errorMessage = "OrderSend failed - Error: " + IntegerToString(GetLastError());
            if(attempt < m_maxRetries)
               Sleep(m_retryDelay);
         }
      }

      return success;
   }

   // Close position (full or partial)
   bool ClosePosition(ulong ticket, double volume, string &errorMessage)
   {
      if(!PositionSelectByTicket(ticket))
      {
         errorMessage = "Position not found: " + IntegerToString(ticket);
         return false;
      }

      // Get position info
      string symbol = PositionGetString(POSITION_SYMBOL);
      double positionVolume = PositionGetDouble(POSITION_VOLUME);
      ENUM_POSITION_TYPE positionType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

      // Validate volume
      if(volume > positionVolume)
         volume = positionVolume;

      // Prepare close request
      MqlTradeRequest request = {};
      MqlTradeResult result = {};

      request.action = TRADE_ACTION_DEAL;
      request.position = ticket;
      request.symbol = symbol;
      request.volume = volume;
      request.type = (positionType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
      request.price = (positionType == POSITION_TYPE_BUY) ?
                      SymbolInfoDouble(symbol, SYMBOL_BID) :
                      SymbolInfoDouble(symbol, SYMBOL_ASK);
      request.deviation = m_slippage;
      request.magic = m_magicNumber;

      // Execute close with retry logic
      bool success = false;
      for(int attempt = 1; attempt <= m_maxRetries; attempt++)
      {
         ZeroMemory(result);

         if(OrderSend(request, result))
         {
            if(result.retcode == TRADE_RETCODE_DONE)
            {
               success = true;
               LogInfo("Position closed successfully - Ticket: " + IntegerToString(ticket) +
                       ", Volume: " + DoubleToString(volume, 2) +
                       ", Price: " + DoubleToString(result.price, _Digits));

               // Update current volume if partial close
               if(volume < positionVolume)
                  m_currentVolume = positionVolume - volume;
               else
                  m_currentVolume = 0.0;

               break;
            }
            else
            {
               errorMessage = "Close failed - Code: " + IntegerToString(result.retcode) +
                             ", Comment: " + result.comment;

               if(!ShouldRetry(result.retcode))
                  break;

               if(attempt < m_maxRetries)
               {
                  LogInfo("Close attempt " + IntegerToString(attempt) + " failed, retrying...");
                  Sleep(m_retryDelay);
               }
            }
         }
         else
         {
            errorMessage = "OrderSend failed - Error: " + IntegerToString(GetLastError());
            if(attempt < m_maxRetries)
               Sleep(m_retryDelay);
         }
      }

      return success;
   }

   // Check if position exists
   bool PositionExists(ulong ticket)
   {
      return PositionSelectByTicket(ticket);
   }

   // Get current position info
   bool GetPositionInfo(ulong ticket, double &volume, double &openPrice, double &currentSL, double &currentTP)
   {
      if(!PositionSelectByTicket(ticket))
         return false;

      volume = PositionGetDouble(POSITION_VOLUME);
      openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
      currentSL = PositionGetDouble(POSITION_SL);
      currentTP = PositionGetDouble(POSITION_TP);

      return true;
   }

   // Get current ticket
   ulong GetCurrentTicket() const { return m_currentTicket; }

   // Check if trade executor is ready
   bool IsReady() const { return m_initialized; }

   // REMOVED: Duplicate PositionExists function

private:
   // Validate order parameters
   bool ValidateOrderParameters(const SCRTSetup &crtSetup, double volume, string &errorMessage)
   {
      // Check volume limits
      double minVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      double maxVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

      if(volume < minVolume || volume > maxVolume)
      {
         errorMessage = "Invalid volume: " + DoubleToString(volume, 2) +
                       " (Min: " + DoubleToString(minVolume, 2) +
                       ", Max: " + DoubleToString(maxVolume, 2) + ")";
         return false;
      }

      // Check if market is open
      if(!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE))
      {
         errorMessage = "Trading not allowed for symbol: " + _Symbol;
         return false;
      }

      // Check account permissions
      if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED))
      {
         errorMessage = "Trading not allowed for account";
         return false;
      }

      return true;
   }

   // Prepare trade request
   bool PrepareTradeRequest(const SCRTSetup &crtSetup, double volume, MqlTradeRequest &request, string &errorMessage)
   {
      ZeroMemory(request);

      request.action = TRADE_ACTION_DEAL;
      request.symbol = _Symbol;
      request.volume = volume;
      request.deviation = m_slippage;
      request.magic = m_magicNumber;
      request.type_filling = ORDER_FILLING_FOK;

      // Set order type and price
      if(crtSetup.entryType == "Long")
      {
         request.type = ORDER_TYPE_BUY;
         request.price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      }
      else
      {
         request.type = ORDER_TYPE_SELL;
         request.price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
      }

      // Set SL and TP
      request.sl = crtSetup.slTarget;
      request.tp = crtSetup.tpTarget;

      // Validate price
      if(request.price <= 0)
      {
         errorMessage = "Invalid price: " + DoubleToString(request.price, _Digits);
         return false;
      }

      return true;
   }

   // Check if retry is appropriate for the error code (IMPROVED)
   bool ShouldRetry(uint retcode)
   {
      switch(retcode)
      {
         // Retryable errors (network, price, timing issues)
         case TRADE_RETCODE_REQUOTE:
         case TRADE_RETCODE_CONNECTION:
         case TRADE_RETCODE_PRICE_CHANGED:
         case TRADE_RETCODE_TIMEOUT:
         case TRADE_RETCODE_PRICE_OFF:
         case TRADE_RETCODE_INVALID_FILL:        // ADDED: Invalid fill policy
         case TRADE_RETCODE_TOO_MANY_REQUESTS:   // ADDED: Too many requests
            return true;

         // Non-retryable errors (configuration, account, market issues)
         case TRADE_RETCODE_INVALID_VOLUME:
         case TRADE_RETCODE_INVALID_PRICE:
         case TRADE_RETCODE_INVALID_STOPS:
         case TRADE_RETCODE_TRADE_DISABLED:
         case TRADE_RETCODE_MARKET_CLOSED:
         case TRADE_RETCODE_NO_MONEY:
         case TRADE_RETCODE_POSITION_CLOSED:
         case TRADE_RETCODE_REJECT:              // ADDED: Request rejected
         case TRADE_RETCODE_CANCEL:              // ADDED: Request cancelled
            return false;

         default:
            // Conservative approach: don't retry unknown errors
            LogInfo("Unknown error code for retry decision: " + IntegerToString(retcode));
            return false;
      }
   }
};

// REMOVED: CStatePersistence class (Phase 9 removed per user request)









//+------------------------------------------------------------------+
//| HTF Data Handler Class                                          |
//+------------------------------------------------------------------+
class CHTFDataHandler
{
private:
   ENUM_TIMEFRAMES m_htfPeriod;              // Higher timeframe period
   int             m_htfATRHandle;            // HTF ATR handle
   double          m_htfATRBuffer[];          // HTF ATR buffer
   MqlRates        m_htfRates[];              // HTF rates buffer
   SBarInfo        m_currentBar;              // Current HTF bar
   SBarInfo        m_previousBar;             // Previous HTF bar
   datetime        m_lastBarTime;             // Last processed bar time
   bool            m_initialized;             // Initialization flag

public:
   // Constructor
   CHTFDataHandler(ENUM_TIMEFRAMES htfPeriod)
   {
      m_htfPeriod = htfPeriod;
      m_htfATRHandle = INVALID_HANDLE;
      m_lastBarTime = 0;
      m_initialized = false;
      ArraySetAsSeries(m_htfATRBuffer, true);
      ArraySetAsSeries(m_htfRates, true);
   }

   // Destructor
   ~CHTFDataHandler()
   {
      if(m_htfATRHandle != INVALID_HANDLE)
      {
         IndicatorRelease(m_htfATRHandle);
         m_htfATRHandle = INVALID_HANDLE;
      }
   }

   // Initialize HTF data handler
   bool Initialize()
   {
      // CRITICAL FIX: Check if HTF is same as current timeframe
      int currentTFSeconds = PeriodSeconds(PERIOD_CURRENT);
      int htfSeconds = PeriodSeconds(m_htfPeriod);

      if(htfSeconds <= currentTFSeconds)
      {
         LogInfo("HTF same as current timeframe - using current timeframe data");
         // Use current timeframe for analysis when HTF is same
      }

      // Create HTF ATR indicator (FIXED: Use InpATRLength instead of 50)
      m_htfATRHandle = iATR(_Symbol, m_htfPeriod, InpATRLength);
      if(m_htfATRHandle == INVALID_HANDLE)
      {
         LogError("Failed to create HTF ATR indicator handle for timeframe: " + EnumToString(m_htfPeriod));
         return false;
      }

      // Initialize arrays (CRITICAL FIX: Add missing array initialization)
      ArraySetAsSeries(m_htfRates, true);
      ArraySetAsSeries(m_htfATRBuffer, true);

      // Wait for indicator to calculate
      if(!WaitForIndicator(m_htfATRHandle))
      {
         LogError("HTF ATR indicator failed to initialize");
         return false;
      }

      // Initialize with current data
      if(!UpdateHTFData())
      {
         LogError("Failed to initialize HTF data");
         return false;
      }

      m_initialized = true;
      LogInfo("HTF Data Handler initialized for timeframe: " + EnumToString(m_htfPeriod) +
              (htfSeconds <= currentTFSeconds ? " (same as current)" : ""));
      return true;
   }

   // Update HTF data (COMPREHENSIVE ERROR LOGGING)
   bool UpdateHTFData()
   {
      // CRITICAL FIX: Remove m_initialized check during initialization
      // This function is called during initialization, so m_initialized will be false
      Print(g_logPrefix + "[DEBUG] UpdateHTFData called - m_initialized: " + (m_initialized ? "true" : "false"));

      // COMPREHENSIVE LOGGING: Log all parameters (with Print fallbacks)
      string startMsg = "UpdateHTFData starting - Symbol: " + _Symbol +
                       ", HTF Period: " + EnumToString(m_htfPeriod) +
                       ", ATR Handle: " + IntegerToString(m_htfATRHandle);
      LogInfo(startMsg);
      Print(g_logPrefix + "[DEBUG] " + startMsg);

      // Check if arrays are properly initialized
      string arrayMsg = "Array sizes before copy - Rates: " + IntegerToString(ArraySize(m_htfRates)) +
                       ", ATR: " + IntegerToString(ArraySize(m_htfATRBuffer));
      LogInfo(arrayMsg);
      Print(g_logPrefix + "[DEBUG] " + arrayMsg);

      // CRITICAL FIX: Reduce data requirements for backtester compatibility
      int requiredBars = 1; // Further reduced to 1 for maximum compatibility

      // STEP 1: Copy HTF rates with comprehensive logging
      LogInfo("Attempting to copy " + IntegerToString(requiredBars) + " HTF rates...");
      ResetLastError(); // Clear any previous errors

      int ratesCopied = CopyRates(_Symbol, m_htfPeriod, 0, requiredBars, m_htfRates);
      int ratesError = GetLastError();

      LogInfo("CopyRates result - Copied: " + IntegerToString(ratesCopied) +
              ", Required: " + IntegerToString(requiredBars) +
              ", Error: " + IntegerToString(ratesError));

      if(ratesCopied < requiredBars)
      {
         string errorMsg = "CRITICAL: Failed to copy HTF rates. Symbol: " + _Symbol +
                          ", Timeframe: " + EnumToString(m_htfPeriod) +
                          ", Requested: " + IntegerToString(requiredBars) +
                          ", Copied: " + IntegerToString(ratesCopied) +
                          ", Error Code: " + IntegerToString(ratesError);
         LogError(errorMsg);
         Print(g_logPrefix + "[CRITICAL] " + errorMsg);

         // Check if symbol and timeframe are valid
         bool symbolExists = SymbolSelect(_Symbol, true);
         string symbolMsg = "Symbol validation - Exists: " + (symbolExists ? "true" : "false");
         LogInfo(symbolMsg);
         Print(g_logPrefix + "[DEBUG] " + symbolMsg);

         return false;
      }

      LogInfo("HTF rates copied successfully: " + IntegerToString(ratesCopied) + " bars");

      // STEP 2: Copy HTF ATR values with comprehensive logging
      LogInfo("Attempting to copy " + IntegerToString(requiredBars) + " HTF ATR values...");
      LogInfo("ATR Handle status - Handle: " + IntegerToString(m_htfATRHandle) +
              ", Calculated bars: " + IntegerToString(BarsCalculated(m_htfATRHandle)));

      ResetLastError(); // Clear any previous errors

      int atrCopied = CopyBuffer(m_htfATRHandle, 0, 0, requiredBars, m_htfATRBuffer);
      int atrError = GetLastError();

      LogInfo("CopyBuffer result - Copied: " + IntegerToString(atrCopied) +
              ", Required: " + IntegerToString(requiredBars) +
              ", Error: " + IntegerToString(atrError));

      if(atrCopied < requiredBars)
      {
         string atrErrorMsg = "CRITICAL: Failed to copy HTF ATR buffer. Handle: " + IntegerToString(m_htfATRHandle) +
                             ", Calculated: " + IntegerToString(BarsCalculated(m_htfATRHandle)) +
                             ", Requested: " + IntegerToString(requiredBars) +
                             ", Copied: " + IntegerToString(atrCopied) +
                             ", Error Code: " + IntegerToString(atrError);
         LogError(atrErrorMsg);
         Print(g_logPrefix + "[CRITICAL] " + atrErrorMsg);
         return false;
      }

      LogInfo("HTF ATR copied successfully: " + IntegerToString(atrCopied) + " values");

      // STEP 3: Validate copied data
      if(ArraySize(m_htfRates) < 1 || ArraySize(m_htfATRBuffer) < 1)
      {
         LogError("CRITICAL: Array sizes invalid after copy - Rates: " + IntegerToString(ArraySize(m_htfRates)) +
                  ", ATR: " + IntegerToString(ArraySize(m_htfATRBuffer)));
         return false;
      }

      // STEP 4: Validate data values
      if(m_htfRates[0].high <= 0 || m_htfRates[0].low <= 0 || m_htfATRBuffer[0] <= 0)
      {
         LogError("CRITICAL: Invalid data values - High: " + DoubleToString(m_htfRates[0].high, 5) +
                  ", Low: " + DoubleToString(m_htfRates[0].low, 5) +
                  ", ATR: " + DoubleToString(m_htfATRBuffer[0], 5));
         return false;
      }

      // Store previous bar data
      m_previousBar = m_currentBar;

      // Update current bar data
      m_currentBar.open = m_htfRates[0].open;
      m_currentBar.high = m_htfRates[0].high;
      m_currentBar.low = m_htfRates[0].low;
      m_currentBar.close = m_htfRates[0].close;
      m_currentBar.atr = m_htfATRBuffer[0];

      // Calculate True Range (simple range for single bar)
      m_currentBar.trueRange = m_currentBar.high - m_currentBar.low;

      LogInfo("HTF data updated successfully - OHLC: " +
              DoubleToString(m_currentBar.open, 5) + "/" +
              DoubleToString(m_currentBar.high, 5) + "/" +
              DoubleToString(m_currentBar.low, 5) + "/" +
              DoubleToString(m_currentBar.close, 5) +
              ", ATR: " + DoubleToString(m_currentBar.atr, 5) +
              ", TR: " + DoubleToString(m_currentBar.trueRange, 5));
      return true;
   }

   // Check if new HTF bar formed
   bool IsNewHTFBar()
   {
      if(!m_initialized)
         return false;

      datetime currentBarTime = m_htfRates[0].time;
      if(currentBarTime != m_lastBarTime)
      {
         m_lastBarTime = currentBarTime;
         return true;
      }
      return false;
   }

   // Get current HTF bar
   SBarInfo GetCurrentBar() const { return m_currentBar; }

   // Get previous HTF bar
   SBarInfo GetPreviousBar() const { return m_previousBar; }

   // Check if HTF data is valid
   bool IsDataValid() const
   {
      return m_initialized &&
             m_currentBar.high > 0 &&
             m_currentBar.low > 0 &&
             m_currentBar.atr > 0;
   }

private:
   // Calculate True Range for HTF bar
   double CalculateTrueRange(int index)
   {
      if(ArraySize(m_htfRates) <= index + 1)
         return 0.0;

      double high = m_htfRates[index].high;
      double low = m_htfRates[index].low;
      double prevClose = m_htfRates[index + 1].close;

      double tr1 = high - low;
      double tr2 = MathAbs(high - prevClose);
      double tr3 = MathAbs(low - prevClose);

      return MathMax(tr1, MathMax(tr2, tr3));
   }

   // Wait for indicator to be ready (FIXED: Better backtester compatibility)
   bool WaitForIndicator(int handle)
   {
      if(handle == INVALID_HANDLE)
      {
         LogError("Invalid indicator handle");
         return false;
      }

      int attempts = 0;
      int maxAttempts = 200; // Increased for backtester
      int requiredBars = 1;  // Reduced requirement for backtester

      while(BarsCalculated(handle) < requiredBars && attempts < maxAttempts)
      {
         Sleep(10); // Reduced sleep time
         attempts++;

         if(attempts % 50 == 0) // Log every 50 attempts
         {
            LogInfo("Waiting for indicator... Attempt: " + IntegerToString(attempts) +
                    ", Calculated: " + IntegerToString(BarsCalculated(handle)));
         }
      }

      int calculated = BarsCalculated(handle);
      if(calculated >= requiredBars)
      {
         LogInfo("Indicator ready - Calculated bars: " + IntegerToString(calculated));
         return true;
      }
      else
      {
         LogError("Indicator failed to initialize after " + IntegerToString(attempts) +
                  " attempts. Calculated: " + IntegerToString(calculated));
         return false;
      }
   }
};

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade         g_trade;                    // Trade execution object
CPositionInfo  g_position;                 // Position information object
COrderInfo     g_order;                    // Order information object

// HTF Data Handler
CHTFDataHandler* g_htfHandler = NULL;       // HTF data handler instance

// FVG & Order Block Detectors
CFVGDetector* g_fvgDetector = NULL;         // FVG detector instance
COrderBlockDetector* g_obDetector = NULL;   // Order Block detector instance

// CRT State Machine
CCRTStateMachine* g_crtStateMachine = NULL; // CRT state machine instance

// Entry Validator
CEntryValidator* g_entryValidator = NULL;   // Entry validator instance

// Risk Manager
CRiskManager* g_riskManager = NULL;         // Risk manager instance

// REMOVED: Partial TP Manager (user request)

// Trade Executor
CTradeExecutor* g_tradeExecutor = NULL;       // Trade executor instance

// REMOVED: State Persistence and Performance Monitor (Phase 9 removed per user request)

// EA State Variables
bool           g_isInitialized = false;    // EA initialization status
datetime       g_lastBarTime = 0;          // Last processed bar time
int            g_magicNumber = 123456;      // Magic number for EA trades
string         g_eaComment = "CRT_EA";      // EA comment for trades

// Configuration Variables
double         g_bulkyCandleATR = 2.1;     // Bulky candle ATR multiplier
double         g_slATRMult = 8.0;          // Stop loss ATR multiplier
// REMOVED: Partial TP global variables (user request)

// Market Data Variables
int            g_atrHandle = INVALID_HANDLE;  // ATR indicator handle
double         g_atrBuffer[];                 // ATR values buffer

// HTF Data Variables
int            g_htfATRHandle = INVALID_HANDLE;  // HTF ATR indicator handle
double         g_htfATRBuffer[];                 // HTF ATR values buffer
MqlRates       g_htfRates[];                     // HTF rates buffer
SBarInfo       g_currentHTFBar;                  // Current HTF bar
SBarInfo       g_previousHTFBar;                 // Previous HTF bar
bool           g_htfDataInitialized = false;     // HTF data initialization flag

// Bulky Candle Detection Variables
bool           g_newBulkyCandle = false;         // New bulky candle detected flag
double         g_lastHigh = 0.0;                 // Last bulky candle high
double         g_lastLow = 0.0;                  // Last bulky candle low
datetime       g_lastHTFBarTime = 0;             // Last HTF bar time processed

// Logging Variables
bool           g_debugMode = false;        // Debug mode flag
string         g_logPrefix = "[CRT_EA] ";  // Log message prefix

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print(g_logPrefix + "Initializing CRT Expert Advisor v0.10");
   
   // Validate input parameters
   if(!ValidateInputParameters())
   {
      Print(g_logPrefix + "ERROR: Invalid input parameters");
      return INIT_PARAMETERS_INCORRECT;
   }

   // Validate HTF timeframe
   if(!ValidateHTFTimeframe())
   {
      Print(g_logPrefix + "ERROR: Invalid higher timeframe setting");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // Initialize configuration
   if(!InitializeConfiguration())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize configuration");
      return INIT_FAILED;
   }
   
   // Initialize market data
   if(!InitializeMarketData())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize market data");
      return INIT_FAILED;
   }
   
   // Initialize trade management
   if(!InitializeTradeManagement())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize trade management");
      return INIT_FAILED;
   }
   
   g_isInitialized = true;
   Print(g_logPrefix + "Initialization completed successfully");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print(g_logPrefix + "Deinitializing CRT Expert Advisor");

   // Clean up indicators
   if(g_atrHandle != INVALID_HANDLE)
   {
      IndicatorRelease(g_atrHandle);
      g_atrHandle = INVALID_HANDLE;
   }

   // Clean up HTF data handler
   if(g_htfHandler != NULL)
   {
      delete g_htfHandler;
      g_htfHandler = NULL;
   }

   // Clean up FVG detector
   if(g_fvgDetector != NULL)
   {
      delete g_fvgDetector;
      g_fvgDetector = NULL;
   }

   // Clean up Order Block detector
   if(g_obDetector != NULL)
   {
      delete g_obDetector;
      g_obDetector = NULL;
   }

   // Clean up CRT State Machine
   if(g_crtStateMachine != NULL)
   {
      delete g_crtStateMachine;
      g_crtStateMachine = NULL;
   }

   // Clean up Entry Validator
   if(g_entryValidator != NULL)
   {
      delete g_entryValidator;
      g_entryValidator = NULL;
   }

   // Clean up Risk Manager
   if(g_riskManager != NULL)
   {
      delete g_riskManager;
      g_riskManager = NULL;
   }

   // REMOVED: Partial TP Manager cleanup (user request)

   // Clean up Trade Executor
   if(g_tradeExecutor != NULL)
   {
      delete g_tradeExecutor;
      g_tradeExecutor = NULL;
   }

   // Save state if needed
   SaveEAState();

   // REMOVED: State Persistence and Performance Monitor cleanup (Phase 9 removed per user request)

   Print(g_logPrefix + "Deinitialization completed. Reason: " + IntegerToString(reason));
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if EA is properly initialized
   if(!g_isInitialized)
      return;
   
   // Check for new bar
   if(!IsNewBar())
      return;
   
   // Update market data
   if(!UpdateMarketData())
   {
      Print(g_logPrefix + "ERROR: Failed to update market data");
      return;
   }
   
   // Main strategy logic (placeholder for future phases)
   ProcessCRTStrategy();

   // Update trade management
   ManageActiveTrades();

   // Periodic state save
   PeriodicStateSave();
}

//+------------------------------------------------------------------+
//| Validate input parameters                                       |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
   bool isValid = true;
   
   // Validate R:R parameters
   if(InpMinRR < 1.0 || InpMinRR > 6.0)
   {
      Print(g_logPrefix + "ERROR: Minimum R:R must be between 1.0 and 6.0");
      isValid = false;
   }
   
   if(InpMaxRR < 1.5 || InpMaxRR > 10.0)
   {
      Print(g_logPrefix + "ERROR: Maximum R:R must be between 1.5 and 10.0");
      isValid = false;
   }
   
   if(InpMinRR >= InpMaxRR)
   {
      Print(g_logPrefix + "ERROR: Minimum R:R must be less than Maximum R:R");
      isValid = false;
   }
   
   // REMOVED: Partial TP validation (user request)
   
   // Validate time parameters
   if(InpCustomTimeStart < 0 || InpCustomTimeStart > 23 || 
      InpCustomTimeEnd < 0 || InpCustomTimeEnd > 23)
   {
      Print(g_logPrefix + "ERROR: Custom time hours must be between 0 and 23");
      isValid = false;
   }
   
   // Validate risk parameters
   if(InpMaxRiskPercent <= 0 || InpMaxRiskPercent > 10.0)
   {
      Print(g_logPrefix + "ERROR: Maximum risk percent must be between 0 and 10%");
      isValid = false;
   }
   
   if(InpLotSize <= 0)
   {
      Print(g_logPrefix + "ERROR: Lot size must be greater than 0");
      isValid = false;
   }

   // ADDED: Validate new parameters
   if(InpATRLength < 1 || InpATRLength > 100)
   {
      Print(g_logPrefix + "ERROR: ATR Length must be between 1 and 100");
      isValid = false;
   }

   if(InpSwingLength < 3 || InpSwingLength > 45)
   {
      Print(g_logPrefix + "ERROR: Swing Length must be between 3 and 45");
      isValid = false;
   }

   return isValid;
}

//+------------------------------------------------------------------+
//| Initialize configuration                                         |
//+------------------------------------------------------------------+
bool InitializeConfiguration()
{
   // Set bulky candle ATR multiplier based on size setting
   switch(InpBulkyCandleSize)
   {
      case CANDLE_BIG:    g_bulkyCandleATR = 2.1; break;
      case CANDLE_NORMAL: g_bulkyCandleATR = 1.6; break;
      case CANDLE_SMALL:  g_bulkyCandleATR = 1.3; break;
      default:            g_bulkyCandleATR = 2.1; break;
   }
   
   // Set stop loss ATR multiplier based on risk amount
   switch(InpRiskAmount)
   {
      case RISK_HIGHEST: g_slATRMult = 10.0; break;
      case RISK_HIGH:    g_slATRMult = 8.0;  break;
      case RISK_NORMAL:  g_slATRMult = 6.5;  break;
      case RISK_LOW:     g_slATRMult = 5.0;  break;
      case RISK_LOWEST:  g_slATRMult = 3.0;  break;
      default:           g_slATRMult = 8.0;  break;
   }
   
   // Set magic number and comment
   g_trade.SetExpertMagicNumber(g_magicNumber);
   
   Print(g_logPrefix + "Configuration initialized:");
   Print(g_logPrefix + "- Bulky Candle ATR: " + DoubleToString(g_bulkyCandleATR, 2));
   Print(g_logPrefix + "- SL ATR Multiplier: " + DoubleToString(g_slATRMult, 2));
   
   return true;
}

//+------------------------------------------------------------------+
//| Initialize market data                                           |
//+------------------------------------------------------------------+
bool InitializeMarketData()
{
   // Initialize ATR indicator (FIXED: Use InpATRLength instead of 50)
   g_atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpATRLength);
   if(g_atrHandle == INVALID_HANDLE)
   {
      Print(g_logPrefix + "ERROR: Failed to create ATR indicator handle");
      return false;
   }

   // Initialize ATR buffer
   ArraySetAsSeries(g_atrBuffer, true);

   // Initialize HTF data handler
   g_htfHandler = new CHTFDataHandler(InpHigherTF);
   if(g_htfHandler == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create HTF data handler");
      return false;
   }

   if(!g_htfHandler.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize HTF data handler");
      delete g_htfHandler;
      g_htfHandler = NULL;
      return false;
   }

   // Initialize FVG detector
   g_fvgDetector = new CFVGDetector(50); // Track up to 50 FVGs
   if(g_fvgDetector == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create FVG detector");
      return false;
   }

   if(!g_fvgDetector.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize FVG detector");
      delete g_fvgDetector;
      g_fvgDetector = NULL;
      return false;
   }

   // Initialize Order Block detector (FIXED: Use parameter-based constructor)
   g_obDetector = new COrderBlockDetector(50); // Track up to 50 OBs, swing length from InpSwingLength
   if(g_obDetector == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create Order Block detector");
      return false;
   }

   if(!g_obDetector.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize Order Block detector");
      delete g_obDetector;
      g_obDetector = NULL;
      return false;
   }

   // Initialize CRT State Machine
   g_crtStateMachine = new CCRTStateMachine(InpMaxConcurrentTrades);
   if(g_crtStateMachine == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create CRT state machine");
      return false;
   }

   if(!g_crtStateMachine.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize CRT state machine");
      delete g_crtStateMachine;
      g_crtStateMachine = NULL;
      return false;
   }

   // Initialize Entry Validator
   g_entryValidator = new CEntryValidator();
   if(g_entryValidator == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create entry validator");
      return false;
   }

   if(!g_entryValidator.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize entry validator");
      delete g_entryValidator;
      g_entryValidator = NULL;
      return false;
   }

   // Initialize Risk Manager
   g_riskManager = new CRiskManager();
   if(g_riskManager == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create risk manager");
      return false;
   }

   if(!g_riskManager.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize risk manager");
      delete g_riskManager;
      g_riskManager = NULL;
      return false;
   }

   // REMOVED: Partial TP Manager initialization (user request)

   // Initialize Trade Executor
   g_tradeExecutor = new CTradeExecutor();
   if(g_tradeExecutor == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create trade executor");
      return false;
   }

   if(!g_tradeExecutor.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize trade executor");
      delete g_tradeExecutor;
      g_tradeExecutor = NULL;
      return false;
   }

   // REMOVED: State Persistence and Performance Monitor initialization (Phase 9 removed per user request)

   // REMOVED: State loading (Phase 9 removed per user request)
   LogInfo("Starting with fresh state");

   Print(g_logPrefix + "Market data initialized successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Initialize trade management                                      |
//+------------------------------------------------------------------+
bool InitializeTradeManagement()
{
   // Set trade parameters
   g_trade.SetDeviationInPoints(10);
   g_trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   Print(g_logPrefix + "Trade management initialized successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Check for new bar                                               |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(currentBarTime != g_lastBarTime)
   {
      g_lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Update market data                                              |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
   // Update ATR values
   if(CopyBuffer(g_atrHandle, 0, 0, 3, g_atrBuffer) < 3)
   {
      Print(g_logPrefix + "ERROR: Failed to copy ATR buffer");
      return false;
   }

   // Update HTF data
   if(g_htfHandler != NULL)
   {
      if(!g_htfHandler.UpdateHTFData())
      {
         LogError("Failed to update HTF data");
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Detect new bulky candle (equivalent to Pine Script logic)      |
//+------------------------------------------------------------------+
bool DetectBulkyCandle()
{
   if(g_htfHandler == NULL || !g_htfHandler.IsDataValid())
      return false;

   // Reset bulky candle flag
   g_newBulkyCandle = false;

   // Check if new HTF bar formed
   if(!g_htfHandler.IsNewHTFBar())
      return false;

   // Get current and previous HTF bars
   SBarInfo currentBar = g_htfHandler.GetCurrentBar();
   SBarInfo previousBar = g_htfHandler.GetPreviousBar();

   // Check if we have valid previous bar data
   if(previousBar.high <= 0 || previousBar.low <= 0)
      return false;

   // Pine Script logic: oldHigherTFBar.h != higherTFBar.h and (higherTFBar.tr > higherTFBar.atr * bulkyCandleATR)
   bool newHTFBar = (previousBar.high != currentBar.high);
   bool sizeCriteria = (currentBar.trueRange > currentBar.atr * g_bulkyCandleATR);

   if(newHTFBar && sizeCriteria)
   {
      g_newBulkyCandle = true;
      g_lastHigh = currentBar.high;
      g_lastLow = currentBar.low;

      LogInfo("New Bulky Candle Detected - High: " + DoubleToString(g_lastHigh, _Digits) +
              ", Low: " + DoubleToString(g_lastLow, _Digits) +
              ", TR: " + DoubleToString(currentBar.trueRange, _Digits) +
              ", ATR: " + DoubleToString(currentBar.atr, _Digits) +
              ", Multiplier: " + DoubleToString(g_bulkyCandleATR, 2));

      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Get bulky candle detection status                               |
//+------------------------------------------------------------------+
bool IsNewBulkyCandle()
{
   return g_newBulkyCandle;
}

//+------------------------------------------------------------------+
//| Get last bulky candle high                                      |
//+------------------------------------------------------------------+
double GetLastBulkyHigh()
{
   return g_lastHigh;
}

//+------------------------------------------------------------------+
//| Get last bulky candle low                                       |
//+------------------------------------------------------------------+
double GetLastBulkyLow()
{
   return g_lastLow;
}

//+------------------------------------------------------------------+
//| Validate HTF timeframe setting                                  |
//+------------------------------------------------------------------+
bool ValidateHTFTimeframe()
{
   // Check if higher timeframe is actually higher than current
   int currentTFSeconds = PeriodSeconds(PERIOD_CURRENT);
   int htfSeconds = PeriodSeconds(InpHigherTF);

   // CRITICAL FIX: Add detailed logging for debugging
   Print(g_logPrefix + "HTF Validation - Current TF: " + EnumToString(PERIOD_CURRENT) +
         " (" + IntegerToString(currentTFSeconds) + "s), HTF: " + EnumToString(InpHigherTF) +
         " (" + IntegerToString(htfSeconds) + "s)");

   if(htfSeconds <= currentTFSeconds)
   {
      Print(g_logPrefix + "WARNING: HTF same as or lower than current timeframe!");
      Print(g_logPrefix + "Current: " + EnumToString(PERIOD_CURRENT) + " (" + IntegerToString(currentTFSeconds) + "s)");
      Print(g_logPrefix + "HTF: " + EnumToString(InpHigherTF) + " (" + IntegerToString(htfSeconds) + "s)");

      // CRITICAL FIX: Auto-adjust HTF for common scenarios
      ENUM_TIMEFRAMES suggestedHTF = GetSuggestedHTF(PERIOD_CURRENT);
      if(suggestedHTF != PERIOD_CURRENT)
      {
         Print(g_logPrefix + "SUGGESTION: Use HTF = " + EnumToString(suggestedHTF) + " for better results");
         Print(g_logPrefix + "For now, continuing with same timeframe (HTF analysis disabled)");
         return true; // Allow same timeframe but disable HTF analysis
      }
      else
      {
         Print(g_logPrefix + "ERROR: Cannot determine suitable HTF. Please use a higher timeframe.");
         return false;
      }
   }

   Print(g_logPrefix + "[INFO] HTF validation passed. Current: " + EnumToString(PERIOD_CURRENT) +
           ", HTF: " + EnumToString(InpHigherTF));
   return true;
}

//+------------------------------------------------------------------+
//| Get suggested HTF based on current timeframe                   |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES GetSuggestedHTF(ENUM_TIMEFRAMES currentTF)
{
   switch(currentTF)
   {
      case PERIOD_M1:  return PERIOD_M5;   // M1 -> M5
      case PERIOD_M2:  return PERIOD_M15;  // M2 -> M15
      case PERIOD_M3:  return PERIOD_M15;  // M3 -> M15
      case PERIOD_M4:  return PERIOD_M15;  // M4 -> M15
      case PERIOD_M5:  return PERIOD_M15;  // M5 -> M15
      case PERIOD_M6:  return PERIOD_M30;  // M6 -> M30
      case PERIOD_M10: return PERIOD_M30;  // M10 -> M30
      case PERIOD_M12: return PERIOD_H1;   // M12 -> H1
      case PERIOD_M15: return PERIOD_H1;   // M15 -> H1
      case PERIOD_M20: return PERIOD_H1;   // M20 -> H1
      case PERIOD_M30: return PERIOD_H4;   // M30 -> H4
      case PERIOD_H1:  return PERIOD_H4;   // H1 -> H4
      case PERIOD_H2:  return PERIOD_D1;   // H2 -> D1
      case PERIOD_H3:  return PERIOD_D1;   // H3 -> D1
      case PERIOD_H4:  return PERIOD_D1;   // H4 -> D1
      case PERIOD_H6:  return PERIOD_W1;   // H6 -> W1
      case PERIOD_H8:  return PERIOD_W1;   // H8 -> W1
      case PERIOD_H12: return PERIOD_W1;   // H12 -> W1
      case PERIOD_D1:  return PERIOD_W1;   // D1 -> W1
      case PERIOD_W1:  return PERIOD_MN1;  // W1 -> MN1
      default:         return currentTF;   // No suggestion
   }
}

//+------------------------------------------------------------------+
//| Main CRT strategy processing                                    |
//+------------------------------------------------------------------+
void ProcessCRTStrategy()
{
   // Phase 2: Bulky candle detection
   DetectBulkyCandle();

   // Phase 3: FVG & Order Block Detection
   bool newFVGDetected = false;
   bool newOBDetected = false;

   if(g_fvgDetector != NULL && g_fvgDetector.IsReady())
   {
      newFVGDetected = g_fvgDetector.DetectFVGs();
   }

   if(g_obDetector != NULL && g_obDetector.IsReady())
   {
      newOBDetected = g_obDetector.DetectOrderBlocks();
   }

   // Log detections for debugging
   if(g_debugMode)
   {
      if(IsNewBulkyCandle())
      {
         LogDebug("New bulky candle detected - High: " + DoubleToString(GetLastBulkyHigh(), _Digits) +
                  ", Low: " + DoubleToString(GetLastBulkyLow(), _Digits));
      }

      if(newFVGDetected)
      {
         LogDebug("New FVG detected");
      }

      if(newOBDetected)
      {
         LogDebug("New Order Block detected");
      }
   }

   // Phase 4: Core Strategy Logic (State Machine)
   if(g_crtStateMachine != NULL && g_crtStateMachine.IsReady())
   {
      g_crtStateMachine.ProcessStateMachine();
   }

   // Log state machine status for debugging
   if(g_debugMode && g_crtStateMachine != NULL && g_crtStateMachine.IsCRTActive())
   {
      SCRTSetup currentCRT = g_crtStateMachine.GetCurrentCRT();
      LogDebug("CRT Active - State: " + IntegerToString(currentCRT.state) +
               ", Overlap: " + IntegerToString(currentCRT.overlapDirection));
   }

   // Placeholder for subsequent phases
   // Phase 5: Entry Validation & Filtering (Enhanced)
   // Phase 6-8: Risk Management & Trade Execution
}

//+------------------------------------------------------------------+
//| Manage active trades (Real Position Management)                 |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
   if(g_tradeExecutor == NULL || !g_tradeExecutor.IsReady())
      return;

   // Check all positions with our magic number
   int totalPositions = PositionsTotal();

   for(int i = totalPositions - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket == 0)
         continue;

      // Check if position belongs to our EA
      if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber)
         continue;

      // Get position details
      string symbol = PositionGetString(POSITION_SYMBOL);
      if(symbol != _Symbol)
         continue;

      ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      double volume = PositionGetDouble(POSITION_VOLUME);
      double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
      double currentSL = PositionGetDouble(POSITION_SL);
      double currentTP = PositionGetDouble(POSITION_TP);
      double currentPrice = (posType == POSITION_TYPE_BUY) ?
                           SymbolInfoDouble(symbol, SYMBOL_BID) :
                           SymbolInfoDouble(symbol, SYMBOL_ASK);

      // Check for emergency exit conditions
      if(CheckEmergencyExit(ticket, posType, openPrice, currentPrice))
         continue;

      // REMOVED: Trailing stop updates (user request)

      // Monitor position health
      MonitorPositionHealth(ticket, posType, volume, openPrice, currentPrice);
   }

   if(g_debugMode && totalPositions > 0)
   {
      LogDebug("Managing " + IntegerToString(totalPositions) + " active positions");
   }
}

//+------------------------------------------------------------------+
//| Check for emergency exit conditions                             |
//+------------------------------------------------------------------+
bool CheckEmergencyExit(ulong ticket, ENUM_POSITION_TYPE posType, double openPrice, double currentPrice)
{
   // Check for maximum loss threshold (emergency stop)
   double maxLossPercent = 10.0; // 10% emergency stop
   double lossPercent = 0.0;

   if(posType == POSITION_TYPE_BUY)
   {
      if(currentPrice < openPrice)
         lossPercent = ((openPrice - currentPrice) / openPrice) * 100.0;
   }
   else
   {
      if(currentPrice > openPrice)
         lossPercent = ((currentPrice - openPrice) / openPrice) * 100.0;
   }

   if(lossPercent > maxLossPercent)
   {
      string closeError = "";
      double volume = PositionGetDouble(POSITION_VOLUME);

      if(g_tradeExecutor.ClosePosition(ticket, volume, closeError))
      {
         LogInfo("Emergency exit executed - Ticket: " + IntegerToString(ticket) +
                 ", Loss: " + DoubleToString(lossPercent, 2) + "%");
         return true;
      }
      else
      {
         LogInfo("Emergency exit failed - Ticket: " + IntegerToString(ticket) +
                 ", Error: " + closeError);
      }
   }

   return false;
}

// REMOVED: UpdatePositionTrailingStop function (user request)

//+------------------------------------------------------------------+
//| Monitor position health                                          |
//+------------------------------------------------------------------+
void MonitorPositionHealth(ulong ticket, ENUM_POSITION_TYPE posType, double volume, double openPrice, double currentPrice)
{
   // Calculate current P&L
   double pnl = 0.0;
   if(posType == POSITION_TYPE_BUY)
      pnl = currentPrice - openPrice;
   else
      pnl = openPrice - currentPrice;

   double pnlPercent = (pnl / openPrice) * 100.0;

   // Log significant moves
   if(MathAbs(pnlPercent) > 1.0) // More than 1% move
   {
      if(g_debugMode)
      {
         LogDebug("Position update - Ticket: " + IntegerToString(ticket) +
                  ", P&L: " + DoubleToString(pnlPercent, 2) + "%");
      }
   }

   // Check for position correlation (future enhancement)
   // Monitor for news events (future enhancement)
   // Check account health (future enhancement)
}

//+------------------------------------------------------------------+
//| Save EA state (REMOVED - Phase 9 removed per user request)    |
//+------------------------------------------------------------------+
void SaveEAState()
{
   // REMOVED: State persistence functionality (Phase 9 removed per user request)
   // This function is kept as a stub to avoid breaking references
}

//+------------------------------------------------------------------+
//| Load EA state (REMOVED - Phase 9 removed per user request)    |
//+------------------------------------------------------------------+
bool LoadEAState()
{
   // REMOVED: State persistence functionality (Phase 9 removed per user request)
   // This function is kept as a stub to avoid breaking references
   return false;
}

//+------------------------------------------------------------------+
//| Check if saved state is still valid                            |
//+------------------------------------------------------------------+
bool IsStateStillValid(const SCRTSetup &savedCRT)
{
   // Check if state is too old (more than 24 hours)
   if(TimeCurrent() - savedCRT.startTime > 86400)
   {
      LogInfo("Saved state is too old - discarding");
      return false;
   }

   // Check if we have an active position that no longer exists
   if(savedCRT.positionTicket > 0)
   {
      if(g_tradeExecutor != NULL && g_tradeExecutor.IsReady())
      {
         if(!g_tradeExecutor.PositionExists(savedCRT.positionTicket))
         {
            LogInfo("Position from saved state no longer exists - discarding");
            return false;
         }
      }
   }

   // Check if state is in a terminal condition
   if(savedCRT.state == CRT_DONE || savedCRT.state == CRT_ABORTED)
   {
      LogInfo("Saved state is in terminal condition - discarding");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Periodic state save (REMOVED - Phase 9 removed per user request) |
//+------------------------------------------------------------------+
void PeriodicStateSave()
{
   // REMOVED: State persistence functionality (Phase 9 removed per user request)
   // This function is kept as a stub to avoid breaking references
}

//+------------------------------------------------------------------+
//| Utility function: Get current ATR value                        |
//+------------------------------------------------------------------+
double GetCurrentATR()
{
   if(ArraySize(g_atrBuffer) > 0)
      return g_atrBuffer[0];
   return 0.0;
}

//+------------------------------------------------------------------+
//| Utility function: Get latest FVG                               |
//+------------------------------------------------------------------+
SFVGInfo GetLatestFVG(bool bullish = true)
{
   SFVGInfo emptyFVG = {};

   if(g_fvgDetector != NULL && g_fvgDetector.IsReady())
   {
      return g_fvgDetector.GetLatestFVG(bullish);
   }

   return emptyFVG;
}

//+------------------------------------------------------------------+
//| Utility function: Get latest Order Block                       |
//+------------------------------------------------------------------+
SOrderBlockInfo GetLatestOB(bool bullish = true)
{
   SOrderBlockInfo emptyOB = {};

   if(g_obDetector != NULL && g_obDetector.IsReady())
   {
      return g_obDetector.GetLatestOB(bullish);
   }

   return emptyOB;
}

//+------------------------------------------------------------------+
//| Utility function: Check if FVG is valid                        |
//+------------------------------------------------------------------+
bool IsFVGValid(const SFVGInfo &fvg)
{
   return (fvg.max > 0 && fvg.min > 0 && fvg.endTime == 0);
}

//+------------------------------------------------------------------+
//| Utility function: Check if Order Block is valid               |
//+------------------------------------------------------------------+
bool IsOBValid(const SOrderBlockInfo &ob)
{
   return (ob.top > 0 && ob.bottom > 0 && !ob.breaker);
}

//+------------------------------------------------------------------+
//| Utility function: Get current CRT setup                        |
//+------------------------------------------------------------------+
SCRTSetup GetCurrentCRTSetup()
{
   SCRTSetup emptyCRT = {};

   if(g_crtStateMachine != NULL && g_crtStateMachine.IsReady())
   {
      return g_crtStateMachine.GetCurrentCRT();
   }

   return emptyCRT;
}

//+------------------------------------------------------------------+
//| Utility function: Check if CRT is active                       |
//+------------------------------------------------------------------+
bool IsCRTActive()
{
   if(g_crtStateMachine != NULL && g_crtStateMachine.IsReady())
   {
      return g_crtStateMachine.IsCRTActive();
   }

   return false;
}

//+------------------------------------------------------------------+
//| Utility function: Get CRT state string                         |
//+------------------------------------------------------------------+
string GetCRTStateString(ENUM_CRT_STATE state)
{
   switch(state)
   {
      case CRT_WAITING_FOR_BULKY_CANDLE:     return "Waiting For Bulky Candle";
      case CRT_WAITING_FOR_SIDE_RETEST:      return "Waiting For Side Retest";
      case CRT_WAITING_FOR_FVG:              return "Waiting For FVG";
      case CRT_WAITING_FOR_OB:               return "Waiting For OB";
      case CRT_WAITING_FOR_FVG_RETRACEMENT:  return "Waiting For FVG Retracement";
      case CRT_WAITING_FOR_OB_RETRACEMENT:   return "Waiting For OB Retracement";
      case CRT_ENTER_POSITION:               return "Enter Position";
      case CRT_ENTRY_TAKEN:                  return "Entry Taken";
      case CRT_STOP_LOSS:                    return "Stop Loss";
      case CRT_TAKE_PROFIT:                  return "Take Profit";
      case CRT_ABORTED:                      return "Aborted";
      case CRT_DONE:                         return "Done";
      default:                               return "Unknown";
   }
}

//+------------------------------------------------------------------+
//| Utility function: Get overlap direction string                 |
//+------------------------------------------------------------------+
string GetOverlapDirectionString(ENUM_OVERLAP_DIRECTION direction)
{
   switch(direction)
   {
      case OVERLAP_BULL:  return "Bull";
      case OVERLAP_BEAR:  return "Bear";
      case OVERLAP_NONE:  return "None";
      default:            return "Unknown";
   }
}

//+------------------------------------------------------------------+
//| Utility function: Validate entry with enhanced system         |
//+------------------------------------------------------------------+
bool ValidateEntryEnhanced(const SCRTSetup &crtSetup, string &rejectionReason)
{
   if(g_entryValidator != NULL && g_entryValidator.IsReady())
   {
      return g_entryValidator.ValidateEntry(crtSetup, rejectionReason);
   }

   // Fallback to basic validation
   rejectionReason = "Entry validator not available";
   return false;
}

//+------------------------------------------------------------------+
//| Utility function: Calculate intelligent position size          |
//+------------------------------------------------------------------+
double CalculateIntelligentPositionSize(const SCRTSetup &crtSetup)
{
   if(g_entryValidator != NULL && g_entryValidator.IsReady())
   {
      return g_entryValidator.CalculatePositionSize(crtSetup);
   }

   // Fallback to basic position sizing
   return CalculateLotSize(0, 0);
}

//+------------------------------------------------------------------+
//| Utility function: Get entry validation status                  |
//+------------------------------------------------------------------+
bool IsEntryValidatorReady()
{
   return (g_entryValidator != NULL && g_entryValidator.IsReady());
}

//+------------------------------------------------------------------+
//| Utility function: Get market condition status                  |
//+------------------------------------------------------------------+
string GetMarketConditionStatus()
{
   if(!IsEntryValidatorReady())
      return "Entry validator not ready";

   string rejectionReason = "";
   SCRTSetup dummySetup = {};

   // Test market conditions only (not full entry validation)
   long currentSpreadLong = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
   double currentSpread = (double)currentSpreadLong;
   if(currentSpread > 30.0) // 3 pips
      return "Spread too high: " + DoubleToString(currentSpread, 1);

   if(!IsValidTradingTime())
      return "Time filter active";

   if(!IsVolumeValid())
      return "Volume too low";

   return "Market conditions OK";
}

//+------------------------------------------------------------------+
//| Utility function: Calculate optimal position size              |
//+------------------------------------------------------------------+
double CalculateOptimalPositionSize(const SCRTSetup &crtSetup)
{
   if(g_riskManager != NULL && g_riskManager.IsReady())
   {
      double stopLossDistance = MathAbs(crtSetup.entryPrice - crtSetup.slTarget);
      return g_riskManager.CalculatePositionSize(crtSetup, stopLossDistance);
   }

   // Fallback to basic position sizing
   return CalculateLotSize(0, 0);
}

//+------------------------------------------------------------------+
//| Utility function: Validate risk limits                         |
//+------------------------------------------------------------------+
bool ValidateRiskLimits(const SCRTSetup &crtSetup, double positionSize, string &rejectionReason)
{
   if(g_riskManager != NULL && g_riskManager.IsReady())
   {
      return g_riskManager.ValidateRiskLimits(crtSetup, positionSize, rejectionReason);
   }

   // Fallback validation
   rejectionReason = "Risk manager not available";
   return false;
}

// REMOVED: UpdateTrailingStop utility function (user request)

//+------------------------------------------------------------------+
//| Utility function: Calculate R:R ratio                          |
//+------------------------------------------------------------------+
double CalculateRRRatio(const SCRTSetup &crtSetup)
{
   if(g_riskManager != NULL && g_riskManager.IsReady())
   {
      return g_riskManager.CalculateRR(crtSetup);
   }

   // Fallback R:R calculation
   if(crtSetup.entryPrice == 0 || crtSetup.slTarget == 0 || crtSetup.exitPrice == 0)
      return 0.0;

   double risk = MathAbs(crtSetup.entryPrice - crtSetup.slTarget);
   double reward = (crtSetup.entryType == "Long") ?
                   (crtSetup.exitPrice - crtSetup.entryPrice) :
                   (crtSetup.entryPrice - crtSetup.exitPrice);

   return (risk > 0) ? reward / risk : 0.0;
}

// REMOVED: Duplicate GetPerformanceStats function

//+------------------------------------------------------------------+
//| Utility function: Check if risk manager is ready              |
//+------------------------------------------------------------------+
bool IsRiskManagerReady()
{
   return (g_riskManager != NULL && g_riskManager.IsReady());
}

//+------------------------------------------------------------------+
//| Utility function: Update performance statistics                |
//+------------------------------------------------------------------+
void UpdatePerformanceStats(const SCRTSetup &crtSetup)
{
   if(g_riskManager != NULL && g_riskManager.IsReady())
   {
      g_riskManager.UpdatePerformanceStats(crtSetup);
   }
}

// REMOVED: Partial TP utility functions (user request)

//+------------------------------------------------------------------+
//| Utility function: Calculate trade result                        |
//+------------------------------------------------------------------+
void CalculateTradeResult(SCRTSetup &crtSetup)
{
   // Calculate actual R:R and trade result
   crtSetup.actualRR = CalculateRRRatio(crtSetup);
   crtSetup.tradeResult = (crtSetup.actualRR >= 0) ? "Win" : "Loss";
}

// REMOVED: GetIntelligentPartialPercentages function (user request)

// REMOVED: IsBreakevenProtectionActive function (user request)

//+------------------------------------------------------------------+
//| Utility function: Execute market order                         |
//+------------------------------------------------------------------+
bool ExecuteMarketOrder(const SCRTSetup &crtSetup, double volume, string &errorMessage)
{
   if(g_tradeExecutor != NULL && g_tradeExecutor.IsReady())
   {
      return g_tradeExecutor.ExecuteMarketOrder(crtSetup, volume, errorMessage);
   }

   errorMessage = "Trade executor not available";
   return false;
}

//+------------------------------------------------------------------+
//| Utility function: Close position                               |
//+------------------------------------------------------------------+
bool ClosePosition(ulong ticket, double volume, string &errorMessage)
{
   if(g_tradeExecutor != NULL && g_tradeExecutor.IsReady())
   {
      return g_tradeExecutor.ClosePosition(ticket, volume, errorMessage);
   }

   errorMessage = "Trade executor not available";
   return false;
}

//+------------------------------------------------------------------+
//| Utility function: Modify position                              |
//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double newSL, double newTP, string &errorMessage)
{
   if(g_tradeExecutor != NULL && g_tradeExecutor.IsReady())
   {
      return g_tradeExecutor.ModifyPosition(ticket, newSL, newTP, errorMessage);
   }

   errorMessage = "Trade executor not available";
   return false;
}

//+------------------------------------------------------------------+
//| Utility function: Check if trade executor is ready            |
//+------------------------------------------------------------------+
bool IsTradeExecutorReady()
{
   return (g_tradeExecutor != NULL && g_tradeExecutor.IsReady());
}

//+------------------------------------------------------------------+
//| Utility function: Get active positions count                   |
//+------------------------------------------------------------------+
int GetActivePositionsCount()
{
   int count = 0;
   int totalPositions = PositionsTotal();

   for(int i = 0; i < totalPositions; i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber &&
            PositionGetString(POSITION_SYMBOL) == _Symbol)
         {
            count++;
         }
      }
   }

   return count;
}

//+------------------------------------------------------------------+
//| Utility function: Get total position volume                    |
//+------------------------------------------------------------------+
double GetTotalPositionVolume()
{
   double totalVolume = 0.0;
   int totalPositions = PositionsTotal();

   for(int i = 0; i < totalPositions; i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber &&
            PositionGetString(POSITION_SYMBOL) == _Symbol)
         {
            totalVolume += PositionGetDouble(POSITION_VOLUME);
         }
      }
   }

   return totalVolume;
}

//+------------------------------------------------------------------+
//| Utility function: Get position status                          |
//+------------------------------------------------------------------+
string GetPositionStatus()
{
   int activePositions = GetActivePositionsCount();
   double totalVolume = GetTotalPositionVolume();

   if(activePositions == 0)
      return "No active positions";

   return "Active positions: " + IntegerToString(activePositions) +
          " | Total volume: " + DoubleToString(totalVolume, 2);
}

// REMOVED: All persistence and performance monitoring utility functions (Phase 9 removed per user request)

//+------------------------------------------------------------------+
//| Utility function: Get system status                            |
//+------------------------------------------------------------------+
string GetSystemStatus()
{
   string status = "=== CRT EA System Status ===\n";

   // Core systems
   status += "HTF Handler: " + (g_htfHandler != NULL ? "Ready" : "Not Ready") + "\n";
   status += "FVG Detector: " + (g_fvgDetector != NULL ? "Ready" : "Not Ready") + "\n";
   status += "OB Detector: " + (g_obDetector != NULL ? "Ready" : "Not Ready") + "\n";
   status += "State Machine: " + (g_crtStateMachine != NULL ? "Ready" : "Not Ready") + "\n";
   status += "Entry Validator: " + (IsEntryValidatorReady() ? "Ready" : "Not Ready") + "\n";
   status += "Risk Manager: " + (IsRiskManagerReady() ? "Ready" : "Not Ready") + "\n";
   // REMOVED: Partial TP Manager status (user request)
   status += "Trade Executor: " + (IsTradeExecutorReady() ? "Ready" : "Not Ready") + "\n";
   // REMOVED: State Persistence and Performance Monitor status (Phase 9 removed per user request)

   // Trading status
   status += "\n--- Trading Status ---\n";
   status += GetPositionStatus() + "\n";
   status += GetMarketConditionStatus() + "\n";

   // REMOVED: Performance status (Phase 9 removed per user request)

   status += "========================\n";

   return status;
}

//+------------------------------------------------------------------+
//| Utility function: Calculate lot size based on risk             |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskAmount, double stopLossDistance)
{
   if(InpUseFixedLots)
      return InpLotSize;

   // Risk-based position sizing (placeholder)
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskMoney = accountBalance * (InpMaxRiskPercent / 100.0);
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   if(stopLossDistance > 0 && tickValue > 0 && tickSize > 0)
   {
      double lotSize = riskMoney / (stopLossDistance * tickValue / tickSize);
      double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
      double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
      lotSize = MathFloor(lotSize / lotStep) * lotStep;

      return lotSize;
   }

   return InpLotSize;
}

//+------------------------------------------------------------------+
//| Utility function: Validate time-based filtering                |
//+------------------------------------------------------------------+
bool IsValidTradingTime()
{
   if(!InpUseTimeFiltering)
      return true;

   bool valid = true;
   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);
   int currentHour = timeStruct.hour;
   int currentDayOfWeek = timeStruct.day_of_week;

   // Weekend filter
   if(InpAvoidWeekends && (currentDayOfWeek == 0 || currentDayOfWeek == 6)) // Sunday = 0, Saturday = 6
      valid = false;

   // Lunch hours filter (12:00-13:00)
   if(InpAvoidLunchHours && (currentHour >= 12 && currentHour < 13))
      valid = false;

   // Early morning filter (00:00-06:00)
   if(InpAvoidEarlyMorning && (currentHour >= 0 && currentHour < 6))
      valid = false;

   // Major trading session filters
   if(InpTradeLondonOnly && (currentHour < 8 || currentHour >= 16))
      valid = false;

   if(InpTradeNewYorkOnly && (currentHour < 13 || currentHour >= 21))
      valid = false;

   if(InpAvoidAsianSession && (currentHour >= 21 || currentHour < 6))
      valid = false;

   // Day of week filters
   if(InpAvoidMondays && currentDayOfWeek == 1) // Monday = 1
      valid = false;

   if(InpAvoidFridays && currentDayOfWeek == 5) // Friday = 5
      valid = false;

   // Session timing filters
   if(InpAvoidFirstHour && (currentHour >= 9 && currentHour < 10))
      valid = false;

   if(InpAvoidLastHour && (currentHour >= 16 && currentHour < 17))
      valid = false;

   // Custom time range filter
   if(InpUseCustomTimeRange)
   {
      if(InpCustomTimeStart <= InpCustomTimeEnd)
      {
         // Normal range (e.g., 9-17)
         if(currentHour < InpCustomTimeStart || currentHour >= InpCustomTimeEnd)
            valid = false;
      }
      else
      {
         // Overnight range (e.g., 22-06)
         if(currentHour >= InpCustomTimeEnd && currentHour < InpCustomTimeStart)
            valid = false;
      }
   }

   return valid;
}

//+------------------------------------------------------------------+
//| Utility function: Validate volume confirmation                 |
//+------------------------------------------------------------------+
bool IsVolumeValid()
{
   // REMOVED: Volume confirmation feature per user request
   // User specifically requested to remove volume confirmation
   return true; // Always return true - no volume filtering
}

//+------------------------------------------------------------------+
//| Utility function: Calculate setup quality score               |
//+------------------------------------------------------------------+
double CalculateQualityScore(const SCRTSetup &setup)
{
   // Use enhanced quality scoring if entry validator is available
   if(g_entryValidator != NULL && g_entryValidator.IsReady())
   {
      return g_entryValidator.CalculateEnhancedQualityScore(setup);
   }

   // Fallback to basic quality scoring
   double score = 0.0;

   // Base score from bulky candle range
   double currentATR = GetCurrentATR();
   if(currentATR > 0)
   {
      double bulkyRange = MathAbs(setup.bulkyHigh - setup.bulkyLow);
      double rangeRatio = bulkyRange / currentATR;

      if(rangeRatio >= 2.5)      score += 2.0;  // Excellent range
      else if(rangeRatio >= 2.0) score += 1.5;  // Good range
      else if(rangeRatio >= 1.5) score += 1.0;  // Average range
      else                       score += 0.5;  // Poor range
   }

   // Volume confirmation bonus
   if(IsVolumeValid())
      score += 1.0;

   // Time-based bonus
   if(IsValidTradingTime())
      score += 0.5;

   return MathMin(5.0, MathMax(0.0, score)); // Clamp between 0-5
}

//+------------------------------------------------------------------+
//| Utility function: Calculate dynamic R:R based on quality (FIXED: Exact Pine Script logic) |
//+------------------------------------------------------------------+
double CalculateDynamicRR(double qualityScore)
{
   if(!InpUseEnhancedRR)
      return 0.39; // Original static R:R (DynamicRR)

   // CRITICAL FIX: Use exact Pine Script stepped thresholds instead of linear interpolation
   if(qualityScore >= 4.0)
      return InpMaxRR;  // High quality setups get maximum R:R
   else if(qualityScore >= 3.0)
      return InpMinRR + (InpMaxRR - InpMinRR) * 0.75;  // Medium-high quality
   else if(qualityScore >= 2.0)
      return InpMinRR + (InpMaxRR - InpMinRR) * 0.5;   // Medium quality
   else if(qualityScore >= 1.0)
      return InpMinRR;  // Low quality gets minimum R:R
   else
      return MathMax(1.0, InpMinRR - 0.5);  // Very low quality
}

// REMOVED: Duplicate CalculateRRRatio function

//+------------------------------------------------------------------+
//| Utility function: Format time for logging                      |
//+------------------------------------------------------------------+
string FormatTime(datetime time)
{
   return TimeToString(time, TIME_DATE | TIME_MINUTES);
}

//+------------------------------------------------------------------+
//| Utility function: Log debug message                            |
//+------------------------------------------------------------------+
void LogDebug(string message)
{
   if(g_debugMode)
   {
      Print(g_logPrefix + "[DEBUG] " + message);
   }
}

//+------------------------------------------------------------------+
//| Utility function: Log info message                             |
//+------------------------------------------------------------------+
void LogInfo(string message)
{
   Print(g_logPrefix + "[INFO] " + message);
}

//+------------------------------------------------------------------+
//| Utility function: Log error message                            |
//+------------------------------------------------------------------+
void LogError(string message)
{
   Print(g_logPrefix + "[ERROR] " + message);
}

//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| CRT_EA.mq5                                                       |
//| Candle Range Theory Expert Advisor                               |
//| Converted from TradingView Pine Script Indicator                 |
//+------------------------------------------------------------------+
#property copyright "CRT Strategy - Converted from TradingView"
#property link      "https://github.com/your-repo/crt-ea"
#property version   "0.10"
#property description "Candle Range Theory EA - Identical logic to TradingView indicator"
#property strict

//+------------------------------------------------------------------+
//| Includes                                                         |
//+------------------------------------------------------------------+
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//+------------------------------------------------------------------+
//| Core Data Structures                                            |
//+------------------------------------------------------------------+

// FVG Information Structure (equivalent to FVGInfo type)
struct SFVGInfo
{
   double            max;                    // FVG top level
   double            min;                    // FVG bottom level
   bool              isBull;                 // Bullish FVG flag
   datetime          startTime;              // FVG start time
   double            totalVolume;            // Total volume
   int               startBarIndex;          // Start bar index
   int               endBarIndex;            // End bar index
   datetime          endTime;                // End time
   bool              extendInfinite;         // Extend infinite flag
   bool              combined;               // Combined flag
   string            combinedTimeframesStr;  // Combined timeframes
   bool              disabled;               // Disabled flag
   string            timeframeStr;           // Timeframe string
   double            lowVolume;              // Low volume
   double            highVolume;             // High volume
   bool              isInverse;              // Inverse FVG flag
   datetime          lastTouched;            // Last touched time
   datetime          lastTouchedIFVG;        // Last touched IFVG time
   int               inverseEndIndex;        // Inverse end index
   datetime          inverseEndTime;         // Inverse end time
   double            inverseVolume;          // Inverse volume
};

// Order Block Information Structure (equivalent to orderBlockInfo type)
struct SOrderBlockInfo
{
   double            top;                    // Order block top
   double            bottom;                 // Order block bottom
   double            obVolume;               // Order block volume
   string            obType;                 // Order block type
   datetime          startTime;              // Start time
   double            bbVolume;               // Breaker block volume
   double            obLowVolume;            // OB low volume
   double            obHighVolume;           // OB high volume
   bool              breaker;                // Breaker flag
   datetime          breakTime;              // Break time
   datetime          breakerEndTime;         // Breaker end time
   string            timeframeStr;           // Timeframe string
   bool              disabled;               // Disabled flag
   string            combinedTimeframesStr;  // Combined timeframes
   bool              combined;               // Combined flag
};

// Main CRT Setup Structure (equivalent to CRT type)
struct SCRTSetup
{
   ENUM_CRT_STATE    state;                  // Current state
   datetime          startTime;              // Setup start time

   ENUM_OVERLAP_DIRECTION overlapDirection;  // Overlap direction
   datetime          bulkyTimeLow;           // Bulky candle low time
   datetime          bulkyTimeHigh;          // Bulky candle high time
   double            bulkyHigh;              // Bulky candle high
   double            bulkyLow;               // Bulky candle low
   datetime          breakTime;              // Break time

   SFVGInfo          fvg;                    // FVG information
   datetime          fvgEndTime;             // FVG end time
   SOrderBlockInfo   ob;                     // Order block information

   double            slTarget;               // Stop loss target
   double            tpTarget;               // Take profit target
   string            entryType;              // Entry type (Long/Short)
   datetime          entryTime;              // Entry time
   datetime          exitTime;               // Exit time
   double            entryPrice;             // Entry price
   double            exitPrice;              // Exit price
   int               dayEndedBeforeExit;     // Day ended before exit

   // Enhanced R:R fields
   double            qualityScore;           // Setup quality score
   double            dynamicRR;              // Dynamic R:R ratio

   // Intelligent Partial Profit Taking fields
   double            partialTP1;             // First partial TP level
   double            partialTP2;             // Second partial TP level
   bool              tp1Hit;                 // TP1 hit flag
   bool              tp2Hit;                 // TP2 hit flag
   double            remainingPosition;      // Remaining position percentage
   double            trailingStop;           // Trailing stop level
   bool              trailingActive;         // Trailing stop active flag
   datetime          tp1HitTime;             // TP1 hit time
   datetime          tp2HitTime;             // TP2 hit time

   // Win/Loss Visual System fields
   string            tradeResult;            // Trade result (Win/Loss/Breakeven)
   double            actualRR;               // Actual R:R achieved
   bool              showResult;             // Show result flag

   // Trade management fields
   ulong             positionTicket;         // Position ticket
   ulong             orderTickets[10];       // Order tickets array
   int               orderCount;             // Number of orders
};

// Bar Information Structure (equivalent to barInfo type)
struct SBarInfo
{
   double            open;                   // Open price
   double            high;                   // High price
   double            low;                    // Low price
   double            close;                  // Close price
   double            trueRange;              // True range
   double            atr;                    // ATR value
};

//+------------------------------------------------------------------+
//| Enumerations                                                     |
//+------------------------------------------------------------------+
enum ENUM_CRT_STATE
{
   CRT_WAITING_FOR_BULKY_CANDLE,    // Waiting For Bulky Candle
   CRT_WAITING_FOR_SIDE_RETEST,     // Waiting For Side Retest
   CRT_WAITING_FOR_FVG_OB,          // Waiting For FVG/OB
   CRT_ENTER_POSITION,              // Enter Position
   CRT_ENTRY_TAKEN,                 // Entry Taken
   CRT_ABORTED                      // Aborted
};

enum ENUM_OVERLAP_DIRECTION
{
   OVERLAP_BULL,                    // Bull Overlap
   OVERLAP_BEAR,                    // Bear Overlap
   OVERLAP_NONE                     // No Overlap
};

enum ENUM_ENTRY_MODE
{
   ENTRY_FVGS,                      // FVGs
   ENTRY_ORDER_BLOCKS               // Order Blocks
};

enum ENUM_TPSL_METHOD
{
   TPSL_DYNAMIC,                    // Dynamic
   TPSL_FIXED                       // Fixed
};

enum ENUM_RISK_AMOUNT
{
   RISK_HIGHEST,                    // Highest
   RISK_HIGH,                       // High
   RISK_NORMAL,                     // Normal
   RISK_LOW,                        // Low
   RISK_LOWEST                      // Lowest
};

enum ENUM_CANDLE_SIZE
{
   CANDLE_BIG,                      // Big
   CANDLE_NORMAL,                   // Normal
   CANDLE_SMALL                     // Small
};

//+------------------------------------------------------------------+
//| Input Parameters - General Configuration                         |
//+------------------------------------------------------------------+
input group "General Configuration"
input ENUM_TIMEFRAMES    InpHigherTF = PERIOD_H4;                    // Higher Timeframe
input ENUM_CANDLE_SIZE   InpBulkyCandleSize = CANDLE_BIG;           // HTF Candle Size
input ENUM_ENTRY_MODE    InpEntryMode = ENTRY_FVGS;                 // Entry Mode
input bool               InpRequireRetracement = false;              // Require Retracement
input bool               InpShowHTFLines = true;                     // Show HTF Candle Lines

//+------------------------------------------------------------------+
//| Input Parameters - TP/SL Configuration                          |
//+------------------------------------------------------------------+
input group "TP / SL"
input bool               InpShowTPSL = true;                         // Enabled
input ENUM_TPSL_METHOD   InpTPSLMethod = TPSL_DYNAMIC;              // TP / SL Method
input ENUM_RISK_AMOUNT   InpRiskAmount = RISK_HIGH;                 // Dynamic Risk
input double             InpTPPercent = 0.3;                        // Fixed Take Profit %
input double             InpSLPercent = 0.4;                        // Fixed Stop Loss %

//+------------------------------------------------------------------+
//| Input Parameters - Enhanced R:R System                          |
//+------------------------------------------------------------------+
input group "Enhanced R:R"
input bool               InpUseEnhancedRR = false;                   // Enable Enhanced R:R System
input double             InpMinRR = 1.5;                            // Minimum R:R Ratio (1.0-6.0)
input double             InpMaxRR = 2.5;                            // Maximum R:R Ratio (1.5-10.0)
input double             InpMinQualityScore = 2.0;                   // Minimum Quality Score (0.0-5.0)
input bool               InpRequireVolumeConfirmation = false;       // Require Volume Confirmation

//+------------------------------------------------------------------+
//| Input Parameters - Partial Profit Taking                        |
//+------------------------------------------------------------------+
input group "Partial Profit Taking"
input bool               InpUsePartialTPs = false;                   // Enable Partial TPs
input double             InpPartialTP1Percent = 30.0;               // First TP Percentage (10.0-50.0)
input double             InpPartialTP2Percent = 40.0;               // Second TP Percentage (10.0-60.0)
input bool               InpUseTrailingStop = true;                  // Enable Trailing Stop
input double             InpTrailingATRMult = 2.0;                  // Trailing Stop ATR Multiplier (0.5-5.0)

//+------------------------------------------------------------------+
//| Input Parameters - Time-Based Filtering                         |
//+------------------------------------------------------------------+
input group "Time Filtering"
input bool               InpUseTimeFiltering = false;                // Enable Time Filtering
input bool               InpAvoidWeekends = false;                   // Avoid Weekends
input bool               InpAvoidLunchHours = false;                 // Avoid Lunch Hours (12:00-13:00)
input bool               InpAvoidEarlyMorning = false;               // Avoid Early Morning (00:00-06:00)
input bool               InpUseCustomTimeRange = false;              // Use Custom Time Range
input int                InpCustomTimeStart = 9;                     // Trading Start Hour (0-23)
input int                InpCustomTimeEnd = 17;                      // Trading End Hour (0-23)
input bool               InpTradeLondonOnly = false;                 // Trade London Session Only (08:00-16:00)
input bool               InpTradeNewYorkOnly = false;                // Trade New York Session Only (13:00-21:00)
input bool               InpAvoidAsianSession = false;               // Avoid Asian Session (21:00-06:00)
input bool               InpAvoidMondays = false;                    // Avoid Mondays
input bool               InpAvoidFridays = false;                    // Avoid Fridays
input bool               InpAvoidFirstHour = false;                  // Avoid First Hour (09:00-10:00)
input bool               InpAvoidLastHour = false;                   // Avoid Last Hour (16:00-17:00)

//+------------------------------------------------------------------+
//| Input Parameters - Visual Enhancements                          |
//+------------------------------------------------------------------+
input group "Visual Enhancements"
input bool               InpShowWinLossMarkers = true;               // Show Win/Loss Markers
input bool               InpShowPositionProjection = true;           // Show Position Projection

//+------------------------------------------------------------------+
//| Input Parameters - Risk Management                              |
//+------------------------------------------------------------------+
input group "Risk Management"
input double             InpLotSize = 0.01;                         // Lot Size
input double             InpMaxRiskPercent = 2.0;                    // Maximum Risk Per Trade %
input int                InpMaxConcurrentTrades = 1;                // Maximum Concurrent Trades
input bool               InpUseFixedLots = true;                     // Use Fixed Lot Size

//+------------------------------------------------------------------+
//| Input Parameters - Alerts                                       |
//+------------------------------------------------------------------+
input group "Alerts"
input bool               InpBuyAlertEnabled = true;                  // Buy Signal
input bool               InpSellAlertEnabled = true;                 // Sell Signal
input bool               InpTPAlertEnabled = true;                   // Take-Profit Signal
input bool               InpSLAlertEnabled = true;                   // Stop-Loss Signal

//+------------------------------------------------------------------+
//| FVG Detection Class                                             |
//+------------------------------------------------------------------+
class CFVGDetector
{
private:
   SFVGInfo          m_activeFVGs[];           // Active FVGs array
   int               m_maxFVGs;                // Maximum FVGs to track
   double            m_fvgSensitivity;         // FVG sensitivity multiplier
   int               m_volumeSMA5Handle;       // Volume SMA(5) handle
   int               m_volumeSMA15Handle;      // Volume SMA(15) handle
   double            m_volumeSMA5Buffer[];     // Volume SMA(5) buffer
   double            m_volumeSMA15Buffer[];    // Volume SMA(15) buffer
   bool              m_initialized;            // Initialization flag

public:
   // Constructor
   CFVGDetector(int maxFVGs = 50)
   {
      m_maxFVGs = maxFVGs;
      m_fvgSensitivity = 2.0; // Default "High" sensitivity
      m_volumeSMA5Handle = INVALID_HANDLE;
      m_volumeSMA15Handle = INVALID_HANDLE;
      m_initialized = false;
      ArrayResize(m_activeFVGs, 0);
      ArraySetAsSeries(m_volumeSMA5Buffer, true);
      ArraySetAsSeries(m_volumeSMA15Buffer, true);
   }

   // Destructor
   ~CFVGDetector()
   {
      if(m_volumeSMA5Handle != INVALID_HANDLE)
      {
         IndicatorRelease(m_volumeSMA5Handle);
         m_volumeSMA5Handle = INVALID_HANDLE;
      }
      if(m_volumeSMA15Handle != INVALID_HANDLE)
      {
         IndicatorRelease(m_volumeSMA15Handle);
         m_volumeSMA15Handle = INVALID_HANDLE;
      }
   }

   // Initialize FVG detector
   bool Initialize()
   {
      // Create volume SMA indicators
      m_volumeSMA5Handle = iMA(_Symbol, PERIOD_CURRENT, 5, 0, MODE_SMA, VOLUME_TICK);
      m_volumeSMA15Handle = iMA(_Symbol, PERIOD_CURRENT, 15, 0, MODE_SMA, VOLUME_TICK);

      if(m_volumeSMA5Handle == INVALID_HANDLE || m_volumeSMA15Handle == INVALID_HANDLE)
      {
         LogError("Failed to create volume SMA indicators for FVG detection");
         return false;
      }

      // Wait for indicators to calculate
      if(!WaitForIndicator(m_volumeSMA5Handle) || !WaitForIndicator(m_volumeSMA15Handle))
      {
         LogError("Volume SMA indicators failed to initialize");
         return false;
      }

      // Set FVG sensitivity based on input
      switch(InpEntryMode)
      {
         case ENTRY_FVGS:
            m_fvgSensitivity = 2.0; // High sensitivity
            break;
         default:
            m_fvgSensitivity = 2.0;
            break;
      }

      m_initialized = true;
      LogInfo("FVG Detector initialized successfully");
      return true;
   }

   // Update volume data
   bool UpdateVolumeData()
   {
      if(!m_initialized)
         return false;

      // Copy volume SMA buffers
      if(CopyBuffer(m_volumeSMA5Handle, 0, 0, 5, m_volumeSMA5Buffer) < 5)
      {
         LogError("Failed to copy volume SMA(5) buffer");
         return false;
      }

      if(CopyBuffer(m_volumeSMA15Handle, 0, 0, 5, m_volumeSMA15Buffer) < 5)
      {
         LogError("Failed to copy volume SMA(15) buffer");
         return false;
      }

      return true;
   }

   // Detect new FVGs (main detection function)
   bool DetectFVGs()
   {
      if(!m_initialized)
         return false;

      // Update volume data
      if(!UpdateVolumeData())
         return false;

      // Get current ATR for size validation
      double currentATR = GetCurrentATR();
      if(currentATR <= 0)
         return false;

      // Check volume conditions
      bool bearCondition = CheckVolumeCondition();
      bool bullCondition = bearCondition; // Same condition for both

      // Get OHLC data for 3-candle pattern
      double high0 = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double low0 = iLow(_Symbol, PERIOD_CURRENT, 0);
      double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
      double high2 = iHigh(_Symbol, PERIOD_CURRENT, 2);
      double low2 = iLow(_Symbol, PERIOD_CURRENT, 2);

      // Pine Script FVG detection logic
      // bearFVG = high < low[2] and close[1] < low[2] and bearCondition
      bool bearFVG = (high0 < low2) && (close1 < low2) && bearCondition;

      // bullFVG = low > high[2] and close[1] > high[2] and bullCondition
      bool bullFVG = (low0 > high2) && (close1 > high2) && bullCondition;

      // Create FVGs if detected
      bool newFVGDetected = false;

      if(bearFVG)
      {
         double fvgSize = MathAbs(low2 - high0);
         if(ValidateFVGSize(fvgSize, currentATR))
         {
            CreateFVG(low2, high0, false, TimeCurrent()); // Bearish FVG
            newFVGDetected = true;
            LogInfo("Bearish FVG detected - Top: " + DoubleToString(low2, _Digits) +
                    ", Bottom: " + DoubleToString(high0, _Digits));
         }
      }

      if(bullFVG)
      {
         double fvgSize = MathAbs(low0 - high2);
         if(ValidateFVGSize(fvgSize, currentATR))
         {
            CreateFVG(low0, high2, true, TimeCurrent()); // Bullish FVG
            newFVGDetected = true;
            LogInfo("Bullish FVG detected - Top: " + DoubleToString(low0, _Digits) +
                    ", Bottom: " + DoubleToString(high2, _Digits));
         }
      }

      // Update existing FVGs (check for invalidation)
      UpdateActiveFVGs();

      return newFVGDetected;
   }

   // Get latest FVG
   SFVGInfo GetLatestFVG(bool bullish = true)
   {
      SFVGInfo emptyFVG = {};

      for(int i = 0; i < ArraySize(m_activeFVGs); i++)
      {
         if(m_activeFVGs[i].isBull == bullish && m_activeFVGs[i].endTime == 0)
         {
            return m_activeFVGs[i];
         }
      }

      return emptyFVG;
   }

   // Check if FVG detector is ready
   bool IsReady() const { return m_initialized; }

private:
   // Check volume condition (equivalent to Pine Script bearCondition/bullCondition)
   bool CheckVolumeCondition()
   {
      if(ArraySize(m_volumeSMA5Buffer) < 1 || ArraySize(m_volumeSMA15Buffer) < 1)
         return false;

      double shortTerm = m_volumeSMA5Buffer[0];
      double longTerm = m_volumeSMA15Buffer[0];

      // Pine Script: barSizeSum * fvgSensitivity > atr / 1.5
      // Simplified volume-based condition for now
      return shortTerm > longTerm * 1.1; // 10% above average
   }

   // Validate FVG size (equivalent to Pine Script FVGSizeEnough)
   bool ValidateFVGSize(double fvgSize, double atr)
   {
      // Pine Script: FVGSizeEnough = (FVGSize * fvgSensitivity > atr)
      return (fvgSize * m_fvgSensitivity > atr);
   }

   // Create new FVG
   void CreateFVG(double max, double min, bool isBull, datetime startTime)
   {
      // Resize array if needed
      int currentSize = ArraySize(m_activeFVGs);
      if(currentSize >= m_maxFVGs)
      {
         // Remove oldest FVG
         for(int i = 0; i < currentSize - 1; i++)
         {
            m_activeFVGs[i] = m_activeFVGs[i + 1];
         }
         ArrayResize(m_activeFVGs, currentSize - 1);
      }

      // Add new FVG
      int newSize = ArraySize(m_activeFVGs) + 1;
      ArrayResize(m_activeFVGs, newSize);

      SFVGInfo newFVG = {};
      newFVG.max = max;
      newFVG.min = min;
      newFVG.isBull = isBull;
      newFVG.startTime = startTime;
      newFVG.endTime = 0; // Active FVG
      newFVG.totalVolume = CalculateFVGVolume();

      m_activeFVGs[newSize - 1] = newFVG;
   }

   // Update active FVGs (check for invalidation)
   void UpdateActiveFVGs()
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);

      for(int i = 0; i < ArraySize(m_activeFVGs); i++)
      {
         if(m_activeFVGs[i].endTime != 0)
            continue; // Already invalidated

         // Check FVG invalidation
         // Bullish FVG: invalidated when price touches top (low <= max)
         // Bearish FVG: invalidated when price touches bottom (high >= min)
         if(m_activeFVGs[i].isBull && currentLow <= m_activeFVGs[i].max)
         {
            m_activeFVGs[i].endTime = TimeCurrent();
            LogDebug("Bullish FVG invalidated at " + DoubleToString(m_activeFVGs[i].max, _Digits));
         }
         else if(!m_activeFVGs[i].isBull && currentHigh >= m_activeFVGs[i].min)
         {
            m_activeFVGs[i].endTime = TimeCurrent();
            LogDebug("Bearish FVG invalidated at " + DoubleToString(m_activeFVGs[i].min, _Digits));
         }
      }
   }

   // Calculate FVG volume (3-bar sum)
   double CalculateFVGVolume()
   {
      long vol0 = iVolume(_Symbol, PERIOD_CURRENT, 0);
      long vol1 = iVolume(_Symbol, PERIOD_CURRENT, 1);
      long vol2 = iVolume(_Symbol, PERIOD_CURRENT, 2);

      return (double)(vol0 + vol1 + vol2);
   }

   // Wait for indicator to be ready
   bool WaitForIndicator(int handle)
   {
      int attempts = 0;
      while(BarsCalculated(handle) < 2 && attempts < 100)
      {
         Sleep(50);
         attempts++;
      }
      return BarsCalculated(handle) >= 2;
   }
};

//+------------------------------------------------------------------+
//| Order Block Detection Class                                     |
//+------------------------------------------------------------------+
class COrderBlockDetector
{
private:
   SOrderBlockInfo   m_activeOBs[];             // Active Order Blocks array
   int               m_maxOBs;                   // Maximum OBs to track
   int               m_swingLength;              // Swing detection length
   double            m_maxATRMult;               // Maximum ATR multiplier for OB size
   bool              m_initialized;              // Initialization flag

   // Swing tracking variables
   int               m_swingType;                // Current swing type (0=high, 1=low)
   double            m_topSwingY;                // Top swing price
   int               m_topSwingX;                // Top swing bar index
   bool              m_topSwingCrossed;          // Top swing crossed flag
   double            m_btmSwingY;                // Bottom swing price
   int               m_btmSwingX;                // Bottom swing bar index
   bool              m_btmSwingCrossed;          // Bottom swing crossed flag

public:
   // Constructor
   COrderBlockDetector(int maxOBs = 50, int swingLength = 10)
   {
      m_maxOBs = maxOBs;
      m_swingLength = swingLength;
      m_maxATRMult = 3.0; // Default maximum ATR multiplier
      m_initialized = false;
      m_swingType = 0;
      m_topSwingY = 0;
      m_topSwingX = 0;
      m_topSwingCrossed = false;
      m_btmSwingY = 0;
      m_btmSwingX = 0;
      m_btmSwingCrossed = false;
      ArrayResize(m_activeOBs, 0);
   }

   // Destructor
   ~COrderBlockDetector()
   {
      // No special cleanup needed
   }

   // Initialize Order Block detector
   bool Initialize()
   {
      m_initialized = true;
      LogInfo("Order Block Detector initialized successfully");
      return true;
   }

   // Detect new Order Blocks (main detection function)
   bool DetectOrderBlocks()
   {
      if(!m_initialized)
         return false;

      // Get current ATR for size validation
      double currentATR = GetCurrentATR();
      if(currentATR <= 0)
         return false;

      // Update swing detection
      UpdateSwingDetection();

      // Check for Order Block formation
      bool newOBDetected = false;

      // Check for bullish Order Block (break above swing high)
      double currentClose = iClose(_Symbol, PERIOD_CURRENT, 0);
      if(currentClose > m_topSwingY && !m_topSwingCrossed)
      {
         m_topSwingCrossed = true;
         if(CreateBullishOB(currentATR))
         {
            newOBDetected = true;
            LogInfo("Bullish Order Block detected at swing high: " + DoubleToString(m_topSwingY, _Digits));
         }
      }

      // Check for bearish Order Block (break below swing low)
      if(currentClose < m_btmSwingY && !m_btmSwingCrossed)
      {
         m_btmSwingCrossed = true;
         if(CreateBearishOB(currentATR))
         {
            newOBDetected = true;
            LogInfo("Bearish Order Block detected at swing low: " + DoubleToString(m_btmSwingY, _Digits));
         }
      }

      // Update existing Order Blocks (check for invalidation)
      UpdateActiveOBs();

      return newOBDetected;
   }

   // Get latest Order Block
   SOrderBlockInfo GetLatestOB(bool bullish = true)
   {
      SOrderBlockInfo emptyOB = {};

      for(int i = 0; i < ArraySize(m_activeOBs); i++)
      {
         bool isBullishOB = (m_activeOBs[i].obType == "Bull");
         if(isBullishOB == bullish && !m_activeOBs[i].breaker)
         {
            return m_activeOBs[i];
         }
      }

      return emptyOB;
   }

   // Check if Order Block detector is ready
   bool IsReady() const { return m_initialized; }

private:
   // Update swing detection (equivalent to Pine Script swing logic)
   void UpdateSwingDetection()
   {
      // Get highest and lowest values over swing length
      double upper = 0, lower = 0;

      // Calculate highest high and lowest low over swing period
      for(int i = 0; i < m_swingLength; i++)
      {
         double high_i = iHigh(_Symbol, PERIOD_CURRENT, i);
         double low_i = iLow(_Symbol, PERIOD_CURRENT, i);

         if(i == 0 || high_i > upper)
            upper = high_i;
         if(i == 0 || low_i < lower)
            lower = low_i;
      }

      // Get swing center values
      double hi = iHigh(_Symbol, PERIOD_CURRENT, m_swingLength);
      double li = iLow(_Symbol, PERIOD_CURRENT, m_swingLength);
      int bi = m_swingLength; // Bar index offset

      // Determine swing type
      int newSwingType = (hi > upper) ? 0 : (li < lower) ? 1 : m_swingType;

      // Update swing points when swing type changes
      if(newSwingType == 0 && m_swingType != 0) // New high swing
      {
         m_topSwingY = hi;
         m_topSwingX = bi;
         m_topSwingCrossed = false;
      }

      if(newSwingType == 1 && m_swingType != 1) // New low swing
      {
         m_btmSwingY = li;
         m_btmSwingX = bi;
         m_btmSwingCrossed = false;
      }

      m_swingType = newSwingType;
   }

   // Create bullish Order Block
   bool CreateBullishOB(double atr)
   {
      // Calculate Order Block boundaries (3-bar formation)
      double boxTop = 0, boxBottom = 0;
      datetime boxTime = 0;

      // Find the formation bars around the swing
      for(int i = 0; i < 3; i++)
      {
         double high_i = iHigh(_Symbol, PERIOD_CURRENT, m_topSwingX + i);
         double low_i = iLow(_Symbol, PERIOD_CURRENT, m_topSwingX + i);

         if(i == 0 || high_i > boxTop)
            boxTop = high_i;
         if(i == 0 || low_i < boxBottom)
            boxBottom = low_i;
         if(i == 0)
            boxTime = iTime(_Symbol, PERIOD_CURRENT, m_topSwingX + i);
      }

      // Validate Order Block size
      double obSize = MathAbs(boxTop - boxBottom);
      if(obSize > atr * m_maxATRMult)
      {
         LogDebug("Bullish OB rejected - size too large: " + DoubleToString(obSize, _Digits));
         return false;
      }

      // Create Order Block
      CreateOB(boxTop, boxBottom, "Bull", boxTime);
      return true;
   }

   // Create bearish Order Block
   bool CreateBearishOB(double atr)
   {
      // Calculate Order Block boundaries (3-bar formation)
      double boxTop = 0, boxBottom = 0;
      datetime boxTime = 0;

      // Find the formation bars around the swing
      for(int i = 0; i < 3; i++)
      {
         double high_i = iHigh(_Symbol, PERIOD_CURRENT, m_btmSwingX + i);
         double low_i = iLow(_Symbol, PERIOD_CURRENT, m_btmSwingX + i);

         if(i == 0 || high_i > boxTop)
            boxTop = high_i;
         if(i == 0 || low_i < boxBottom)
            boxBottom = low_i;
         if(i == 0)
            boxTime = iTime(_Symbol, PERIOD_CURRENT, m_btmSwingX + i);
      }

      // Validate Order Block size
      double obSize = MathAbs(boxTop - boxBottom);
      if(obSize > atr * m_maxATRMult)
      {
         LogDebug("Bearish OB rejected - size too large: " + DoubleToString(obSize, _Digits));
         return false;
      }

      // Create Order Block
      CreateOB(boxTop, boxBottom, "Bear", boxTime);
      return true;
   }

   // Create new Order Block
   void CreateOB(double top, double bottom, string obType, datetime startTime)
   {
      // Resize array if needed
      int currentSize = ArraySize(m_activeOBs);
      if(currentSize >= m_maxOBs)
      {
         // Remove oldest OB
         for(int i = 0; i < currentSize - 1; i++)
         {
            m_activeOBs[i] = m_activeOBs[i + 1];
         }
         ArrayResize(m_activeOBs, currentSize - 1);
      }

      // Add new Order Block
      int newSize = ArraySize(m_activeOBs) + 1;
      ArrayResize(m_activeOBs, newSize);

      SOrderBlockInfo newOB = {};
      newOB.top = top;
      newOB.bottom = bottom;
      newOB.obType = obType;
      newOB.startTime = startTime;
      newOB.breaker = false;
      newOB.breakTime = 0;
      newOB.obVolume = CalculateOBVolume();

      m_activeOBs[newSize - 1] = newOB;
   }

   // Update active Order Blocks (check for invalidation)
   void UpdateActiveOBs()
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);
      double currentClose = iClose(_Symbol, PERIOD_CURRENT, 0);

      for(int i = 0; i < ArraySize(m_activeOBs); i++)
      {
         if(m_activeOBs[i].breaker)
            continue; // Already broken

         // Check Order Block invalidation
         if(m_activeOBs[i].obType == "Bull")
         {
            // Bullish OB: invalidated when price breaks below bottom
            if(currentLow < m_activeOBs[i].bottom)
            {
               m_activeOBs[i].breaker = true;
               m_activeOBs[i].breakTime = TimeCurrent();
               LogDebug("Bullish OB broken at " + DoubleToString(m_activeOBs[i].bottom, _Digits));
            }
         }
         else if(m_activeOBs[i].obType == "Bear")
         {
            // Bearish OB: invalidated when price breaks above top
            if(currentHigh > m_activeOBs[i].top)
            {
               m_activeOBs[i].breaker = true;
               m_activeOBs[i].breakTime = TimeCurrent();
               LogDebug("Bearish OB broken at " + DoubleToString(m_activeOBs[i].top, _Digits));
            }
         }
      }
   }

   // Calculate Order Block volume (3-bar sum)
   double CalculateOBVolume()
   {
      long vol0 = iVolume(_Symbol, PERIOD_CURRENT, 0);
      long vol1 = iVolume(_Symbol, PERIOD_CURRENT, 1);
      long vol2 = iVolume(_Symbol, PERIOD_CURRENT, 2);

      return (double)(vol0 + vol1 + vol2);
   }
};

//+------------------------------------------------------------------+
//| HTF Data Handler Class                                          |
//+------------------------------------------------------------------+
class CHTFDataHandler
{
private:
   ENUM_TIMEFRAMES m_htfPeriod;              // Higher timeframe period
   int             m_htfATRHandle;            // HTF ATR handle
   double          m_htfATRBuffer[];          // HTF ATR buffer
   MqlRates        m_htfRates[];              // HTF rates buffer
   SBarInfo        m_currentBar;              // Current HTF bar
   SBarInfo        m_previousBar;             // Previous HTF bar
   datetime        m_lastBarTime;             // Last processed bar time
   bool            m_initialized;             // Initialization flag

public:
   // Constructor
   CHTFDataHandler(ENUM_TIMEFRAMES htfPeriod)
   {
      m_htfPeriod = htfPeriod;
      m_htfATRHandle = INVALID_HANDLE;
      m_lastBarTime = 0;
      m_initialized = false;
      ArraySetAsSeries(m_htfATRBuffer, true);
      ArraySetAsSeries(m_htfRates, true);
   }

   // Destructor
   ~CHTFDataHandler()
   {
      if(m_htfATRHandle != INVALID_HANDLE)
      {
         IndicatorRelease(m_htfATRHandle);
         m_htfATRHandle = INVALID_HANDLE;
      }
   }

   // Initialize HTF data handler
   bool Initialize()
   {
      // Create HTF ATR indicator
      m_htfATRHandle = iATR(_Symbol, m_htfPeriod, 50);
      if(m_htfATRHandle == INVALID_HANDLE)
      {
         LogError("Failed to create HTF ATR indicator handle for timeframe: " + EnumToString(m_htfPeriod));
         return false;
      }

      // Wait for indicator to calculate
      if(!WaitForIndicator(m_htfATRHandle))
      {
         LogError("HTF ATR indicator failed to initialize");
         return false;
      }

      // Initialize with current data
      if(!UpdateHTFData())
      {
         LogError("Failed to initialize HTF data");
         return false;
      }

      m_initialized = true;
      LogInfo("HTF Data Handler initialized for timeframe: " + EnumToString(m_htfPeriod));
      return true;
   }

   // Update HTF data
   bool UpdateHTFData()
   {
      if(!m_initialized)
         return false;

      // Copy HTF rates
      if(CopyRates(_Symbol, m_htfPeriod, 0, 3, m_htfRates) < 3)
      {
         LogError("Failed to copy HTF rates");
         return false;
      }

      // Copy HTF ATR values
      if(CopyBuffer(m_htfATRHandle, 0, 0, 3, m_htfATRBuffer) < 3)
      {
         LogError("Failed to copy HTF ATR buffer");
         return false;
      }

      // Store previous bar data
      m_previousBar = m_currentBar;

      // Update current bar data
      m_currentBar.open = m_htfRates[0].open;
      m_currentBar.high = m_htfRates[0].high;
      m_currentBar.low = m_htfRates[0].low;
      m_currentBar.close = m_htfRates[0].close;
      m_currentBar.atr = m_htfATRBuffer[0];

      // Calculate True Range
      m_currentBar.trueRange = CalculateTrueRange(0);

      return true;
   }

   // Check if new HTF bar formed
   bool IsNewHTFBar()
   {
      if(!m_initialized)
         return false;

      datetime currentBarTime = m_htfRates[0].time;
      if(currentBarTime != m_lastBarTime)
      {
         m_lastBarTime = currentBarTime;
         return true;
      }
      return false;
   }

   // Get current HTF bar
   SBarInfo GetCurrentBar() const { return m_currentBar; }

   // Get previous HTF bar
   SBarInfo GetPreviousBar() const { return m_previousBar; }

   // Check if HTF data is valid
   bool IsDataValid() const
   {
      return m_initialized &&
             m_currentBar.high > 0 &&
             m_currentBar.low > 0 &&
             m_currentBar.atr > 0;
   }

private:
   // Calculate True Range for HTF bar
   double CalculateTrueRange(int index)
   {
      if(ArraySize(m_htfRates) <= index + 1)
         return 0.0;

      double high = m_htfRates[index].high;
      double low = m_htfRates[index].low;
      double prevClose = m_htfRates[index + 1].close;

      double tr1 = high - low;
      double tr2 = MathAbs(high - prevClose);
      double tr3 = MathAbs(low - prevClose);

      return MathMax(tr1, MathMax(tr2, tr3));
   }

   // Wait for indicator to be ready
   bool WaitForIndicator(int handle)
   {
      int attempts = 0;
      while(BarsCalculated(handle) < 2 && attempts < 100)
      {
         Sleep(50);
         attempts++;
      }
      return BarsCalculated(handle) >= 2;
   }
};

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade         g_trade;                    // Trade execution object
CPositionInfo  g_position;                 // Position information object
COrderInfo     g_order;                    // Order information object

// HTF Data Handler
CHTFDataHandler* g_htfHandler = NULL;       // HTF data handler instance

// FVG & Order Block Detectors
CFVGDetector* g_fvgDetector = NULL;         // FVG detector instance
COrderBlockDetector* g_obDetector = NULL;   // Order Block detector instance

// EA State Variables
bool           g_isInitialized = false;    // EA initialization status
datetime       g_lastBarTime = 0;          // Last processed bar time
int            g_magicNumber = 123456;      // Magic number for EA trades
string         g_eaComment = "CRT_EA";      // EA comment for trades

// Configuration Variables
double         g_bulkyCandleATR = 2.1;     // Bulky candle ATR multiplier
double         g_slATRMult = 8.0;          // Stop loss ATR multiplier
double         g_finalTPPercent = 30.0;    // Final TP percentage (calculated)
bool           g_validPartialTPs = true;   // Partial TP validation status

// Market Data Variables
int            g_atrHandle = INVALID_HANDLE;  // ATR indicator handle
double         g_atrBuffer[];                 // ATR values buffer

// HTF Data Variables
int            g_htfATRHandle = INVALID_HANDLE;  // HTF ATR indicator handle
double         g_htfATRBuffer[];                 // HTF ATR values buffer
MqlRates       g_htfRates[];                     // HTF rates buffer
SBarInfo       g_currentHTFBar;                  // Current HTF bar
SBarInfo       g_previousHTFBar;                 // Previous HTF bar
bool           g_htfDataInitialized = false;     // HTF data initialization flag

// Bulky Candle Detection Variables
bool           g_newBulkyCandle = false;         // New bulky candle detected flag
double         g_lastHigh = 0.0;                 // Last bulky candle high
double         g_lastLow = 0.0;                  // Last bulky candle low
datetime       g_lastHTFBarTime = 0;             // Last HTF bar time processed

// Logging Variables
bool           g_debugMode = false;        // Debug mode flag
string         g_logPrefix = "[CRT_EA] ";  // Log message prefix

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print(g_logPrefix + "Initializing CRT Expert Advisor v0.10");
   
   // Validate input parameters
   if(!ValidateInputParameters())
   {
      Print(g_logPrefix + "ERROR: Invalid input parameters");
      return INIT_PARAMETERS_INCORRECT;
   }

   // Validate HTF timeframe
   if(!ValidateHTFTimeframe())
   {
      Print(g_logPrefix + "ERROR: Invalid higher timeframe setting");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // Initialize configuration
   if(!InitializeConfiguration())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize configuration");
      return INIT_FAILED;
   }
   
   // Initialize market data
   if(!InitializeMarketData())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize market data");
      return INIT_FAILED;
   }
   
   // Initialize trade management
   if(!InitializeTradeManagement())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize trade management");
      return INIT_FAILED;
   }
   
   g_isInitialized = true;
   Print(g_logPrefix + "Initialization completed successfully");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print(g_logPrefix + "Deinitializing CRT Expert Advisor");

   // Clean up indicators
   if(g_atrHandle != INVALID_HANDLE)
   {
      IndicatorRelease(g_atrHandle);
      g_atrHandle = INVALID_HANDLE;
   }

   // Clean up HTF data handler
   if(g_htfHandler != NULL)
   {
      delete g_htfHandler;
      g_htfHandler = NULL;
   }

   // Clean up FVG detector
   if(g_fvgDetector != NULL)
   {
      delete g_fvgDetector;
      g_fvgDetector = NULL;
   }

   // Clean up Order Block detector
   if(g_obDetector != NULL)
   {
      delete g_obDetector;
      g_obDetector = NULL;
   }

   // Save state if needed
   SaveEAState();

   Print(g_logPrefix + "Deinitialization completed. Reason: " + IntegerToString(reason));
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if EA is properly initialized
   if(!g_isInitialized)
      return;
   
   // Check for new bar
   if(!IsNewBar())
      return;
   
   // Update market data
   if(!UpdateMarketData())
   {
      Print(g_logPrefix + "ERROR: Failed to update market data");
      return;
   }
   
   // Main strategy logic (placeholder for future phases)
   ProcessCRTStrategy();
   
   // Update trade management
   ManageActiveTrades();
}

//+------------------------------------------------------------------+
//| Validate input parameters                                       |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
   bool isValid = true;
   
   // Validate R:R parameters
   if(InpMinRR < 1.0 || InpMinRR > 6.0)
   {
      Print(g_logPrefix + "ERROR: Minimum R:R must be between 1.0 and 6.0");
      isValid = false;
   }
   
   if(InpMaxRR < 1.5 || InpMaxRR > 10.0)
   {
      Print(g_logPrefix + "ERROR: Maximum R:R must be between 1.5 and 10.0");
      isValid = false;
   }
   
   if(InpMinRR >= InpMaxRR)
   {
      Print(g_logPrefix + "ERROR: Minimum R:R must be less than Maximum R:R");
      isValid = false;
   }
   
   // Validate partial TP percentages
   if(InpPartialTP1Percent + InpPartialTP2Percent > 90.0)
   {
      Print(g_logPrefix + "ERROR: Partial TP percentages cannot exceed 90%");
      g_validPartialTPs = false;
   }
   else
   {
      g_validPartialTPs = true;
      g_finalTPPercent = 100.0 - InpPartialTP1Percent - InpPartialTP2Percent;
   }
   
   // Validate time parameters
   if(InpCustomTimeStart < 0 || InpCustomTimeStart > 23 || 
      InpCustomTimeEnd < 0 || InpCustomTimeEnd > 23)
   {
      Print(g_logPrefix + "ERROR: Custom time hours must be between 0 and 23");
      isValid = false;
   }
   
   // Validate risk parameters
   if(InpMaxRiskPercent <= 0 || InpMaxRiskPercent > 10.0)
   {
      Print(g_logPrefix + "ERROR: Maximum risk percent must be between 0 and 10%");
      isValid = false;
   }
   
   if(InpLotSize <= 0)
   {
      Print(g_logPrefix + "ERROR: Lot size must be greater than 0");
      isValid = false;
   }
   
   return isValid;
}

//+------------------------------------------------------------------+
//| Initialize configuration                                         |
//+------------------------------------------------------------------+
bool InitializeConfiguration()
{
   // Set bulky candle ATR multiplier based on size setting
   switch(InpBulkyCandleSize)
   {
      case CANDLE_BIG:    g_bulkyCandleATR = 2.1; break;
      case CANDLE_NORMAL: g_bulkyCandleATR = 1.6; break;
      case CANDLE_SMALL:  g_bulkyCandleATR = 1.3; break;
      default:            g_bulkyCandleATR = 2.1; break;
   }
   
   // Set stop loss ATR multiplier based on risk amount
   switch(InpRiskAmount)
   {
      case RISK_HIGHEST: g_slATRMult = 10.0; break;
      case RISK_HIGH:    g_slATRMult = 8.0;  break;
      case RISK_NORMAL:  g_slATRMult = 6.5;  break;
      case RISK_LOW:     g_slATRMult = 5.0;  break;
      case RISK_LOWEST:  g_slATRMult = 3.0;  break;
      default:           g_slATRMult = 8.0;  break;
   }
   
   // Set magic number and comment
   g_trade.SetExpertMagicNumber(g_magicNumber);
   
   Print(g_logPrefix + "Configuration initialized:");
   Print(g_logPrefix + "- Bulky Candle ATR: " + DoubleToString(g_bulkyCandleATR, 2));
   Print(g_logPrefix + "- SL ATR Multiplier: " + DoubleToString(g_slATRMult, 2));
   Print(g_logPrefix + "- Valid Partial TPs: " + (g_validPartialTPs ? "Yes" : "No"));
   Print(g_logPrefix + "- Final TP Percent: " + DoubleToString(g_finalTPPercent, 1) + "%");
   
   return true;
}

//+------------------------------------------------------------------+
//| Initialize market data                                           |
//+------------------------------------------------------------------+
bool InitializeMarketData()
{
   // Initialize ATR indicator
   g_atrHandle = iATR(_Symbol, PERIOD_CURRENT, 50);
   if(g_atrHandle == INVALID_HANDLE)
   {
      Print(g_logPrefix + "ERROR: Failed to create ATR indicator handle");
      return false;
   }

   // Initialize ATR buffer
   ArraySetAsSeries(g_atrBuffer, true);

   // Initialize HTF data handler
   g_htfHandler = new CHTFDataHandler(InpHigherTF);
   if(g_htfHandler == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create HTF data handler");
      return false;
   }

   if(!g_htfHandler.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize HTF data handler");
      delete g_htfHandler;
      g_htfHandler = NULL;
      return false;
   }

   // Initialize FVG detector
   g_fvgDetector = new CFVGDetector(50); // Track up to 50 FVGs
   if(g_fvgDetector == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create FVG detector");
      return false;
   }

   if(!g_fvgDetector.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize FVG detector");
      delete g_fvgDetector;
      g_fvgDetector = NULL;
      return false;
   }

   // Initialize Order Block detector
   g_obDetector = new COrderBlockDetector(50, 10); // Track up to 50 OBs, swing length 10
   if(g_obDetector == NULL)
   {
      Print(g_logPrefix + "ERROR: Failed to create Order Block detector");
      return false;
   }

   if(!g_obDetector.Initialize())
   {
      Print(g_logPrefix + "ERROR: Failed to initialize Order Block detector");
      delete g_obDetector;
      g_obDetector = NULL;
      return false;
   }

   Print(g_logPrefix + "Market data initialized successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Initialize trade management                                      |
//+------------------------------------------------------------------+
bool InitializeTradeManagement()
{
   // Set trade parameters
   g_trade.SetDeviationInPoints(10);
   g_trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   Print(g_logPrefix + "Trade management initialized successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Check for new bar                                               |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(currentBarTime != g_lastBarTime)
   {
      g_lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Update market data                                              |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
   // Update ATR values
   if(CopyBuffer(g_atrHandle, 0, 0, 3, g_atrBuffer) < 3)
   {
      Print(g_logPrefix + "ERROR: Failed to copy ATR buffer");
      return false;
   }

   // Update HTF data
   if(g_htfHandler != NULL)
   {
      if(!g_htfHandler.UpdateHTFData())
      {
         LogError("Failed to update HTF data");
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Detect new bulky candle (equivalent to Pine Script logic)      |
//+------------------------------------------------------------------+
bool DetectBulkyCandle()
{
   if(g_htfHandler == NULL || !g_htfHandler.IsDataValid())
      return false;

   // Reset bulky candle flag
   g_newBulkyCandle = false;

   // Check if new HTF bar formed
   if(!g_htfHandler.IsNewHTFBar())
      return false;

   // Get current and previous HTF bars
   SBarInfo currentBar = g_htfHandler.GetCurrentBar();
   SBarInfo previousBar = g_htfHandler.GetPreviousBar();

   // Check if we have valid previous bar data
   if(previousBar.high <= 0 || previousBar.low <= 0)
      return false;

   // Pine Script logic: oldHigherTFBar.h != higherTFBar.h and (higherTFBar.tr > higherTFBar.atr * bulkyCandleATR)
   bool newHTFBar = (previousBar.high != currentBar.high);
   bool sizeCriteria = (currentBar.trueRange > currentBar.atr * g_bulkyCandleATR);

   if(newHTFBar && sizeCriteria)
   {
      g_newBulkyCandle = true;
      g_lastHigh = currentBar.high;
      g_lastLow = currentBar.low;

      LogInfo("New Bulky Candle Detected - High: " + DoubleToString(g_lastHigh, _Digits) +
              ", Low: " + DoubleToString(g_lastLow, _Digits) +
              ", TR: " + DoubleToString(currentBar.trueRange, _Digits) +
              ", ATR: " + DoubleToString(currentBar.atr, _Digits) +
              ", Multiplier: " + DoubleToString(g_bulkyCandleATR, 2));

      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Get bulky candle detection status                               |
//+------------------------------------------------------------------+
bool IsNewBulkyCandle()
{
   return g_newBulkyCandle;
}

//+------------------------------------------------------------------+
//| Get last bulky candle high                                      |
//+------------------------------------------------------------------+
double GetLastBulkyHigh()
{
   return g_lastHigh;
}

//+------------------------------------------------------------------+
//| Get last bulky candle low                                       |
//+------------------------------------------------------------------+
double GetLastBulkyLow()
{
   return g_lastLow;
}

//+------------------------------------------------------------------+
//| Validate HTF timeframe setting                                  |
//+------------------------------------------------------------------+
bool ValidateHTFTimeframe()
{
   // Check if higher timeframe is actually higher than current
   int currentTFSeconds = PeriodSeconds(PERIOD_CURRENT);
   int htfSeconds = PeriodSeconds(InpHigherTF);

   if(htfSeconds <= currentTFSeconds)
   {
      LogError("Higher timeframe must be higher than current timeframe. Current: " +
               EnumToString(PERIOD_CURRENT) + ", HTF: " + EnumToString(InpHigherTF));
      return false;
   }

   LogInfo("HTF validation passed. Current: " + EnumToString(PERIOD_CURRENT) +
           ", HTF: " + EnumToString(InpHigherTF));
   return true;
}

//+------------------------------------------------------------------+
//| Main CRT strategy processing                                    |
//+------------------------------------------------------------------+
void ProcessCRTStrategy()
{
   // Phase 2: Bulky candle detection
   DetectBulkyCandle();

   // Phase 3: FVG & Order Block Detection
   bool newFVGDetected = false;
   bool newOBDetected = false;

   if(g_fvgDetector != NULL && g_fvgDetector.IsReady())
   {
      newFVGDetected = g_fvgDetector.DetectFVGs();
   }

   if(g_obDetector != NULL && g_obDetector.IsReady())
   {
      newOBDetected = g_obDetector.DetectOrderBlocks();
   }

   // Log detections for debugging
   if(g_debugMode)
   {
      if(IsNewBulkyCandle())
      {
         LogDebug("New bulky candle detected - High: " + DoubleToString(GetLastBulkyHigh(), _Digits) +
                  ", Low: " + DoubleToString(GetLastBulkyLow(), _Digits));
      }

      if(newFVGDetected)
      {
         LogDebug("New FVG detected");
      }

      if(newOBDetected)
      {
         LogDebug("New Order Block detected");
      }
   }

   // Placeholder for subsequent phases
   // Phase 4: Core Strategy Logic (State Machine)
   // Phase 5: Entry Validation & Filtering
   // Phase 6-8: Risk Management & Trade Execution
}

//+------------------------------------------------------------------+
//| Manage active trades (placeholder)                              |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
   // Placeholder for trade management logic
   // This will be implemented in subsequent phases
   
   if(g_debugMode)
   {
      Print(g_logPrefix + "Managing active trades - Phase 1 placeholder");
   }
}

//+------------------------------------------------------------------+
//| Save EA state                                                   |
//+------------------------------------------------------------------+
void SaveEAState()
{
   // Placeholder for state persistence
   // This will be implemented in Phase 9
   
   if(g_debugMode)
   {
      Print(g_logPrefix + "Saving EA state - Phase 1 placeholder");
   }
}

//+------------------------------------------------------------------+
//| Utility function: Get current ATR value                        |
//+------------------------------------------------------------------+
double GetCurrentATR()
{
   if(ArraySize(g_atrBuffer) > 0)
      return g_atrBuffer[0];
   return 0.0;
}

//+------------------------------------------------------------------+
//| Utility function: Get latest FVG                               |
//+------------------------------------------------------------------+
SFVGInfo GetLatestFVG(bool bullish = true)
{
   SFVGInfo emptyFVG = {};

   if(g_fvgDetector != NULL && g_fvgDetector.IsReady())
   {
      return g_fvgDetector.GetLatestFVG(bullish);
   }

   return emptyFVG;
}

//+------------------------------------------------------------------+
//| Utility function: Get latest Order Block                       |
//+------------------------------------------------------------------+
SOrderBlockInfo GetLatestOB(bool bullish = true)
{
   SOrderBlockInfo emptyOB = {};

   if(g_obDetector != NULL && g_obDetector.IsReady())
   {
      return g_obDetector.GetLatestOB(bullish);
   }

   return emptyOB;
}

//+------------------------------------------------------------------+
//| Utility function: Check if FVG is valid                        |
//+------------------------------------------------------------------+
bool IsFVGValid(const SFVGInfo &fvg)
{
   return (fvg.max > 0 && fvg.min > 0 && fvg.endTime == 0);
}

//+------------------------------------------------------------------+
//| Utility function: Check if Order Block is valid               |
//+------------------------------------------------------------------+
bool IsOBValid(const SOrderBlockInfo &ob)
{
   return (ob.top > 0 && ob.bottom > 0 && !ob.breaker);
}

//+------------------------------------------------------------------+
//| Utility function: Calculate lot size based on risk             |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskAmount, double stopLossDistance)
{
   if(InpUseFixedLots)
      return InpLotSize;

   // Risk-based position sizing (placeholder)
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskMoney = accountBalance * (InpMaxRiskPercent / 100.0);
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   if(stopLossDistance > 0 && tickValue > 0 && tickSize > 0)
   {
      double lotSize = riskMoney / (stopLossDistance * tickValue / tickSize);
      double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
      double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
      lotSize = MathFloor(lotSize / lotStep) * lotStep;

      return lotSize;
   }

   return InpLotSize;
}

//+------------------------------------------------------------------+
//| Utility function: Validate time-based filtering                |
//+------------------------------------------------------------------+
bool IsValidTradingTime()
{
   if(!InpUseTimeFiltering)
      return true;

   bool valid = true;
   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);
   int currentHour = timeStruct.hour;
   int currentDayOfWeek = timeStruct.day_of_week;

   // Weekend filter
   if(InpAvoidWeekends && (currentDayOfWeek == 0 || currentDayOfWeek == 6)) // Sunday = 0, Saturday = 6
      valid = false;

   // Lunch hours filter (12:00-13:00)
   if(InpAvoidLunchHours && (currentHour >= 12 && currentHour < 13))
      valid = false;

   // Early morning filter (00:00-06:00)
   if(InpAvoidEarlyMorning && (currentHour >= 0 && currentHour < 6))
      valid = false;

   // Major trading session filters
   if(InpTradeLondonOnly && (currentHour < 8 || currentHour >= 16))
      valid = false;

   if(InpTradeNewYorkOnly && (currentHour < 13 || currentHour >= 21))
      valid = false;

   if(InpAvoidAsianSession && (currentHour >= 21 || currentHour < 6))
      valid = false;

   // Day of week filters
   if(InpAvoidMondays && currentDayOfWeek == 1) // Monday = 1
      valid = false;

   if(InpAvoidFridays && currentDayOfWeek == 5) // Friday = 5
      valid = false;

   // Session timing filters
   if(InpAvoidFirstHour && (currentHour >= 9 && currentHour < 10))
      valid = false;

   if(InpAvoidLastHour && (currentHour >= 16 && currentHour < 17))
      valid = false;

   // Custom time range filter
   if(InpUseCustomTimeRange)
   {
      if(InpCustomTimeStart <= InpCustomTimeEnd)
      {
         // Normal range (e.g., 9-17)
         if(currentHour < InpCustomTimeStart || currentHour >= InpCustomTimeEnd)
            valid = false;
      }
      else
      {
         // Overnight range (e.g., 22-06)
         if(currentHour >= InpCustomTimeEnd && currentHour < InpCustomTimeStart)
            valid = false;
      }
   }

   return valid;
}

//+------------------------------------------------------------------+
//| Utility function: Validate volume confirmation                 |
//+------------------------------------------------------------------+
bool IsVolumeValid()
{
   if(!InpRequireVolumeConfirmation)
      return true;

   // Get current volume and average volume
   long currentVolume = iVolume(_Symbol, PERIOD_CURRENT, 0);

   // Calculate average volume over 20 periods
   long totalVolume = 0;
   for(int i = 1; i <= 20; i++)
   {
      totalVolume += iVolume(_Symbol, PERIOD_CURRENT, i);
   }
   double avgVolume = (double)totalVolume / 20.0;

   // Volume must be at least 10% above average
   return (currentVolume >= avgVolume * 1.1);
}

//+------------------------------------------------------------------+
//| Utility function: Calculate setup quality score               |
//+------------------------------------------------------------------+
double CalculateQualityScore(const SCRTSetup &setup)
{
   double score = 0.0;

   // Base score from bulky candle range (placeholder logic)
   double currentATR = GetCurrentATR();
   if(currentATR > 0)
   {
      double bulkyRange = setup.bulkyHigh - setup.bulkyLow;
      double rangeRatio = bulkyRange / currentATR;

      if(rangeRatio >= 2.5)      score += 2.0;  // Excellent range
      else if(rangeRatio >= 2.0) score += 1.5;  // Good range
      else if(rangeRatio >= 1.5) score += 1.0;  // Average range
      else                       score += 0.5;  // Poor range
   }

   // Volume confirmation bonus
   if(IsVolumeValid())
      score += 1.0;

   // Time-based bonus
   if(IsValidTradingTime())
      score += 0.5;

   // Additional quality factors (placeholder)
   // - FVG/OB strength
   // - Market structure alignment
   // - Confluence factors

   return MathMin(5.0, MathMax(0.0, score)); // Clamp between 0-5
}

//+------------------------------------------------------------------+
//| Utility function: Calculate dynamic R:R based on quality      |
//+------------------------------------------------------------------+
double CalculateDynamicRR(double qualityScore)
{
   if(!InpUseEnhancedRR)
      return 0.39; // Original static R:R

   // Linear interpolation between min and max R:R based on quality
   double normalizedScore = qualityScore / 5.0; // Normalize to 0-1
   double dynamicRR = InpMinRR + (normalizedScore * (InpMaxRR - InpMinRR));

   return MathMin(InpMaxRR, MathMax(InpMinRR, dynamicRR));
}

//+------------------------------------------------------------------+
//| Utility function: Calculate R:R ratio achieved                |
//+------------------------------------------------------------------+
double CalculateRRRatio(const SCRTSetup &setup)
{
   if(setup.entryPrice == 0 || setup.slTarget == 0 || setup.exitPrice == 0)
      return 0.0;

   // Calculate risk (distance to stop loss)
   double risk = MathAbs(setup.entryPrice - setup.slTarget);

   // Calculate actual reward/loss (distance to exit)
   double reward = 0.0;
   if(setup.entryType == "Long")
      reward = setup.exitPrice - setup.entryPrice;
   else if(setup.entryType == "Short")
      reward = setup.entryPrice - setup.exitPrice;

   // Return R:R ratio (positive for profit, negative for loss)
   return (risk > 0) ? reward / risk : 0.0;
}

//+------------------------------------------------------------------+
//| Utility function: Format time for logging                      |
//+------------------------------------------------------------------+
string FormatTime(datetime time)
{
   return TimeToString(time, TIME_DATE | TIME_MINUTES);
}

//+------------------------------------------------------------------+
//| Utility function: Log debug message                            |
//+------------------------------------------------------------------+
void LogDebug(string message)
{
   if(g_debugMode)
   {
      Print(g_logPrefix + "[DEBUG] " + message);
   }
}

//+------------------------------------------------------------------+
//| Utility function: Log info message                             |
//+------------------------------------------------------------------+
void LogInfo(string message)
{
   Print(g_logPrefix + "[INFO] " + message);
}

//+------------------------------------------------------------------+
//| Utility function: Log error message                            |
//+------------------------------------------------------------------+
void LogError(string message)
{
   Print(g_logPrefix + "[ERROR] " + message);
}

//+------------------------------------------------------------------+

# CRT Strategy - Complete Usage Guide

## 📋 Overview

The CRT Strategy is a TradingView strategy version of the Candle Range Theory (CRT) indicator. It converts all indicator signals into actual trading orders, enabling live trading and more accurate backtesting with realistic order execution.

## ✅ **SYNTAX ERRORS FIXED**

All Pine Script syntax errors have been resolved:
- ✅ Fixed line continuation issues (proper 2-space indentation)
- ✅ Fixed multi-line input declarations
- ✅ Fixed string concatenation syntax
- ✅ Fixed strategy function call formatting
- ✅ Inlined long parameter lists for better readability
- ✅ **Fixed function scope error** (moved `getPosition` function to global scope)
- ✅ **Strategy now compiles without errors**

## 🚀 Key Features

### ✅ **Preserved from Original Indicator**
- Complete Candle Range Theory logic
- Enhanced R:R system with quality scoring
- Intelligent partial profit taking
- Trailing stop functionality
- Volume confirmation
- FVG and Order Block detection
- All visual elements and backtesting dashboard

### 🆕 **New Strategy Features**
- **Live Trading Capability**: Real order execution through broker integration
- **Advanced Position Sizing**: Multiple sizing methods (% equity, fixed quantity, fixed USD)
- **Realistic Backtesting**: Includes commission, slippage, and proper order execution
- **Strategy Performance Metrics**: Comprehensive performance dashboard
- **Order Management**: Automatic partial TP execution and trailing stops
- **Risk Management**: Built-in position limits and margin requirements

## 📊 Strategy Settings

### **Strategy Settings Group**
- **Position Size Type**: Choose how to calculate position size
  - `Percent of Equity` (Recommended): Uses percentage of account equity
  - `Fixed Quantity`: Uses fixed number of shares/contracts
  - `Fixed USD`: Uses fixed dollar amount
- **Position Size Value**: The value for your chosen sizing method
- **Max Open Trades**: Maximum concurrent positions (default: 1)
- **Enable Strategy Alerts**: Custom alert messages for order fills
- **Commission %**: Commission percentage (default: 0.1%)
- **Slippage (ticks)**: Slippage in ticks (default: 2)

### **Enhanced R:R System** (Optional)
- **Use Enhanced R:R System**: Enable dynamic R:R based on setup quality
- **Minimum R:R Ratio**: Minimum risk-reward for low quality setups (1.5)
- **Maximum R:R Ratio**: Maximum risk-reward for high quality setups (2.5)
- **Require Volume Confirmation**: Require above-average volume
- **Minimum Quality Score**: Minimum setup quality required (1-5 scale)

### **Partial Profit Taking** (Optional)
- **Use Partial Take Profits**: Enable intelligent partial TP system
- **First Partial Size %**: Percentage to close at 1:1 R:R (default: 30%)
- **Second Partial Size %**: Percentage to close at 1.5:1 R:R (default: 40%)
- **Use Trailing Stop**: Enable trailing stop after first partial
- **Trailing Stop ATR Multiplier**: ATR distance for trailing (default: 2.0)

## 🎯 Recommended Settings

### **Conservative Setup** (Lower Risk)
```
Position Size Type: Percent of Equity
Position Size Value: 5-10%
Enhanced R:R: Enabled
Min R:R: 2.0, Max R:R: 3.0
Partial TPs: Enabled (40%/50%/10%)
Volume Confirmation: Enabled
Min Quality Score: 2.5
```

### **Aggressive Setup** (Higher Risk/Reward)
```
Position Size Type: Percent of Equity  
Position Size Value: 15-25%
Enhanced R:R: Enabled
Min R:R: 1.5, Max R:R: 2.5
Partial TPs: Enabled (25%/35%/40%)
Volume Confirmation: Disabled
Min Quality Score: 1.5
```

### **Original Behavior** (Matches Indicator)
```
Enhanced R:R: Disabled
Partial TPs: Disabled
Volume Confirmation: Disabled
Position Size: 10% of Equity
```

## 📈 How It Works

### **1. Setup Detection**
- Waits for bulky candles on higher timeframe
- Calculates setup quality score (if enhanced R:R enabled)
- Validates volume confirmation (if enabled)

### **2. Entry Execution**
- Places market order when CRT entry conditions are met
- Position size calculated based on your settings
- Entry rejected if quality/volume requirements not met

### **3. Exit Management**

#### **Without Partial TPs:**
- Single TP and SL orders placed immediately after entry
- Uses original or enhanced R:R ratios

#### **With Partial TPs:**
- **TP1 (30%)**: Closes at 1:1 R:R (breakeven protection)
- **TP2 (40%)**: Closes at quality-based level (1.4-1.8x)
- **Final TP (30%)**: Closes at enhanced R:R target
- **Trailing Stop**: Activates after TP1, protects remaining position

### **4. Risk Management**
- Automatic stop-loss on all positions
- Position size limits based on equity
- Maximum open trades limit
- Commission and slippage included in calculations

## 🔧 Setup Instructions

### **Step 1: Add Strategy to Chart**
1. Copy the `CRT_strategy` code
2. Open TradingView Pine Editor
3. Paste the code and save as "CRT Strategy"
4. Add to your chart

### **Step 2: Configure Settings**
1. Open strategy settings
2. Configure position sizing based on your risk tolerance
3. Enable/disable enhanced features as desired
4. Set commission and slippage to match your broker

### **Step 3: Backtesting**
1. Set your backtesting period
2. Review performance metrics in the dashboard
3. Optimize settings based on results
4. Test on different timeframes and symbols

### **Step 4: Live Trading** (Optional)
1. Connect TradingView to your broker (if supported)
2. Start with small position sizes
3. Monitor performance closely
4. Adjust settings based on live results

## ⚠️ Important Warnings

### **Risk Management**
- **Never risk more than you can afford to lose**
- Start with small position sizes (1-5% of equity)
- Test thoroughly on paper trading first
- Monitor drawdowns and adjust if necessary

### **Backtesting Limitations**
- Past performance doesn't guarantee future results
- Backtesting may not reflect real market conditions
- Consider market regime changes and volatility
- Test on multiple timeframes and symbols

### **Live Trading Considerations**
- Ensure broker integration is properly configured
- Monitor for execution delays and slippage
- Be aware of market hours and liquidity
- Have a plan for managing open positions

## 📊 Performance Monitoring

### **Key Metrics to Watch**
- **Win Rate**: Should remain high (60%+)
- **Profit Factor**: Should be > 1.5
- **Max Drawdown**: Keep under 20%
- **Average R:R**: Monitor actual vs. expected
- **Trade Frequency**: Ensure sufficient opportunities

### **Dashboard Information**
- **Real-time Performance**: Net profit, win rate, profit factor
- **Position Status**: Current position size and direction
- **System Status**: Shows which features are enabled
- **Configuration**: Displays current settings

## 🔄 Differences from Indicator

### **What's the Same**
- Identical CRT detection logic
- Same enhanced R:R calculations
- Same partial TP and trailing stop logic
- Same visual elements and dashboard

### **What's Different**
- **Real Orders**: Places actual buy/sell orders instead of alerts
- **Position Tracking**: Uses strategy.position_size instead of internal tracking
- **Performance Metrics**: Real strategy performance instead of simulated
- **Risk Management**: Built-in position sizing and risk controls
- **Execution**: Realistic order execution with commission and slippage

## 🆘 Troubleshooting

### **No Trades Executing**
- Check if Enhanced R:R quality score is too high
- Verify volume confirmation isn't too restrictive
- Ensure position size settings are correct
- Check if max open trades limit is reached

### **Poor Performance**
- Review position sizing (may be too large/small)
- Check if partial TP percentages are valid (<90% total)
- Verify commission and slippage settings
- Consider adjusting R:R ratios

### **Strategy Errors**
- Ensure all input values are within valid ranges
- Check that partial TP percentages don't exceed 90%
- Verify timeframe compatibility
- Review broker connection (for live trading)

## 📞 Support

For questions or issues:
1. Review this documentation thoroughly
2. Check TradingView Pine Script documentation
3. Test with different settings on paper trading
4. Start with conservative position sizes

---

**Remember**: This strategy is for educational purposes. Always do your own research and consider consulting with a financial advisor before live trading.

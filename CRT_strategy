//@version=5
strategy("CRT Strategy - Candle Range Theory", 
         shorttitle="CRT Strategy",
         overlay=true, 
         process_orders_on_close=true,
         calc_on_order_fills=true,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=10,
         commission_type=strategy.commission.percent,
         commission_value=0.1,
         slippage=2,
         margin_long=100,
         margin_short=100)

// ============================================================================
// STRATEGY-SPECIFIC INPUTS
// ============================================================================
strategyGroup = "Strategy Settings"
positionSizeType = input.string("Percent of Equity", "Position Size Type", options=["Percent of Equity", "Fixed Quantity", "Fixed USD"], group=strategyGroup, tooltip="How to calculate position size")
positionSizeValue = input.float(10.0, "Position Size Value", minval=0.1, step=0.1, group=strategyGroup, tooltip="Position size value (% for equity, quantity for fixed, USD for fixed USD)")
useStrategyAlerts = input.bool(true, "Enable Strategy Alerts", group=strategyGroup, tooltip="Enable custom alert messages for strategy orders")
maxOpenTrades = input.int(1, "Max Open Trades", minval=1, maxval=10, group=strategyGroup, tooltip="Maximum number of concurrent open positions")

// Commission and Slippage (informational - set in strategy declaration)
commissionPercent = input.float(0.1, "Commission %", minval=0.0, step=0.01, group=strategyGroup, tooltip="Commission percentage (for reference - actual commission set in strategy declaration)")
slippageTicks = input.int(2, "Slippage (ticks)", minval=0, group=strategyGroup, tooltip="Slippage in ticks (for reference - actual slippage set in strategy declaration)")

// ============================================================================
// ORIGINAL CRT INDICATOR CODE (PRESERVED)
// ============================================================================

// Debug
DEBUG = false

// Inputs
higherTF = input.timeframe("1D", "Higher Timeframe", group = "CRT")
entryMode = input.string("FVGs", "Entry Mode", options = ["FVGs", "Order Blocks"], group = "CRT")
requireRetracement = input.bool(true, "Require Retracement", group = "CRT")

// TP/SL
tpslMethod = input.string("Dynamic", "TP/SL Method", options = ["Fixed", "Dynamic"], group = "TP/SL")
tpPercent = input.float(1.0, "Take Profit %", group = "TP/SL")
slPercent = input.float(1.0, "Stop Loss %", group = "TP/SL")
slATRMult = input.float(2.0, "Stop Loss ATR Multiplier", group = "TP/SL")

// Enhanced R:R System
useEnhancedRR = input.bool(false, "Use Enhanced R:R System", group = "Enhanced R:R", tooltip = "Enable dynamic R:R calculation based on setup quality. When disabled, uses original 0.39 R:R.")
minRR = input.float(1.5, "Minimum R:R Ratio", minval = 1.0, maxval = 3.0, step = 0.1, group = "Enhanced R:R", tooltip = "Minimum risk-reward ratio for low quality setups")
maxRR = input.float(2.5, "Maximum R:R Ratio", minval = 1.5, maxval = 4.0, step = 0.1, group = "Enhanced R:R", tooltip = "Maximum risk-reward ratio for high quality setups")
requireVolumeConfirmation = input.bool(false, "Require Volume Confirmation", group = "Enhanced R:R", tooltip = "Require above-average volume for entry")
minQualityScore = input.float(1.0, "Minimum Quality Score", minval = 0.0, maxval = 5.0, step = 0.5, group = "Enhanced R:R", tooltip = "Minimum setup quality score required for entry (1-5 scale)")

// Intelligent Partial Profit Taking System
usePartialTPs = input.bool(false, "Use Partial Take Profits", group = "Partial Profit Taking", tooltip = "Enable intelligent partial profit taking system. Secures profits progressively while letting winners run.")
partialTP1Percent = input.float(30.0, "First Partial Size %", minval = 10.0, maxval = 50.0, step = 5.0, group = "Partial Profit Taking", tooltip = "Percentage of position to close at first partial TP (breakeven protection)")
partialTP2Percent = input.float(40.0, "Second Partial Size %", minval = 20.0, maxval = 60.0, step = 5.0, group = "Partial Profit Taking", tooltip = "Percentage of position to close at second partial TP (profit securing)")
useTrailingStop = input.bool(true, "Use Trailing Stop", group = "Partial Profit Taking", tooltip = "Enable trailing stop after first partial TP is hit")
trailingATRMult = input.float(2.0, "Trailing Stop ATR Multiplier", minval = 1.0, maxval = 5.0, step = 0.5, group = "Partial Profit Taking", tooltip = "ATR multiplier for trailing stop distance")

// Input validation for partial TP percentages
validPartialTPs = (partialTP1Percent + partialTP2Percent) <= 90.0
finalTPPercent = 100.0 - partialTP1Percent - partialTP2Percent

// Original DynamicRR for backward compatibility
DynamicRR = DEBUG ? input.float(0.39, "[DBG] Dynamic Risk:Reward Ratio", group = "Debug") : 0.39

// FVG
showFVG = input.bool(true, "Show FVG", group = "FVG")
fvgExtendStyle = input.string("Until Broken", "FVG Extend Style", options = ["Until Broken", "Fixed Length"], group = "FVG")
fvgFixedLength = input.int(20, "FVG Fixed Length", group = "FVG")

// Order Blocks
showOB = input.bool(true, "Show Order Blocks", group = "Order Blocks")
obExtendStyle = input.string("Until Broken", "OB Extend Style", options = ["Until Broken", "Fixed Length"], group = "Order Blocks")
obFixedLength = input.int(20, "OB Fixed Length", group = "Order Blocks")

// Visuals
showHTFLines = input.bool(true, "Show HTF Lines", group = "Visuals")
showTPSL = input.bool(true, "Show TP/SL", group = "Visuals")
dbgTPSLVersion = input.string("Default", "TP/SL Version", options = ["Default", "Alternative"], group = "Visuals")
lblSize = input.string("Small", "Label Size", options = ["Tiny", "Small", "Normal", "Large", "Huge"], group = "Visuals")

// Colors
highColor = input.color(color.green, "High Color", group = "Colors")
lowColor = input.color(color.red, "Low Color", group = "Colors")
textColor = input.color(color.white, "Text Color", group = "Colors")

// Backtesting
backtestDisplayEnabled = input.bool(true, "Show Backtesting Dashboard", group = "Backtesting")
backtestingLocation = input.string("Top Right", "Backtesting Location",
  options = ["Top Left", "Top Center", "Top Right", "Middle Left", "Middle Center", "Middle Right", "Bottom Left", "Bottom Center", "Bottom Right"],
  group = "Backtesting")
screenerColor = input.color(color.new(color.black, 20), "Screener Color", group = "Backtesting")

// Alerts (kept for compatibility but strategy will use order fills)
buyAlertEnabled = input.bool(true, "Buy Alert", group = "Alerts")
sellAlertEnabled = input.bool(true, "Sell Alert", group = "Alerts")
tpAlertEnabled = input.bool(true, "Take-Profit Alert", group = "Alerts")
slAlertEnabled = input.bool(true, "Stop-Loss Alert", group = "Alerts")

// ============================================================================
// STRATEGY POSITION SIZING LOGIC
// ============================================================================

// Calculate position size based on user selection
getPositionSize() =>
    switch positionSizeType
        "Percent of Equity" => strategy.equity * (positionSizeValue / 100) / close
        "Fixed Quantity" => positionSizeValue
        "Fixed USD" => positionSizeValue / close
        => strategy.equity * 0.1 / close  // Default fallback

// Check if we can open new trades
canOpenTrade() =>
    strategy.opentrades < maxOpenTrades

// ============================================================================
// STRATEGY VARIABLES (REPLACING ALERT TICKS)
// ============================================================================

// Strategy order flags (replacing alert ticks)
var bool strategyLongEntry = false
var bool strategyShortEntry = false
var bool strategyTPExit = false
var bool strategySLExit = false

// Strategy order IDs for tracking
var string currentEntryID = na
var string currentTP1ID = na
var string currentTP2ID = na
var string currentFinalTPID = na
var string currentSLID = na
var string currentTrailID = na

// Position tracking
var bool inPosition = false
var string positionDirection = na
var float entryPrice = na
var bool tp1Executed = false
var bool tp2Executed = false

// ============================================================================
// ORIGINAL CRT HELPER FUNCTIONS
// ============================================================================

// ATR and other calculations
atrCRT = ta.atr(14)
lastHigh = request.security(syminfo.tickerid, higherTF, high[1], lookahead = barmerge.lookahead_off)
lastLow = request.security(syminfo.tickerid, higherTF, low[1], lookahead = barmerge.lookahead_off)
lastClose = request.security(syminfo.tickerid, higherTF, close[1], lookahead = barmerge.lookahead_off)
lastOpen = request.security(syminfo.tickerid, higherTF, open[1], lookahead = barmerge.lookahead_off)

// Bulky candle detection
newBulkyCandle = false
if higherTF == "1D"
    newBulkyCandle := math.abs(lastHigh - lastLow) > atrCRT * 2.1
else if higherTF == "4H"
    newBulkyCandle := math.abs(lastHigh - lastLow) > atrCRT * 1.8
else if higherTF == "1H"
    newBulkyCandle := math.abs(lastHigh - lastLow) > atrCRT * 1.5
else
    newBulkyCandle := math.abs(lastHigh - lastLow) > atrCRT * 2.1

// Utility functions
diffPercent (float val1, float val2) =>
    (math.abs(val1 - val2) / val2) * 100.0

//#region Enhanced R:R Functions
// Calculate setup quality score based on multiple factors
setupQualityScore(bulkyRange, currentVolume, atr) =>
    score = 0.0
    avgVol = ta.sma(volume, 20)

    // Bulky candle size factor (bigger candles = better setups)
    if bulkyRange > atr * 3.0
        score += 3.0
    else if bulkyRange > atr * 2.5
        score += 2.0
    else if bulkyRange > atr * 2.0
        score += 1.0

    // Volume factor (higher volume = better confirmation)
    if currentVolume > avgVol * 1.5
        score += 2.0
    else if currentVolume > avgVol * 1.2
        score += 1.0

    score

// Get dynamic R:R ratio based on setup quality
getDynamicRR(qualityScore) =>
    if useEnhancedRR
        if qualityScore >= 4.0
            maxRR  // High quality setups get maximum R:R
        else if qualityScore >= 3.0
            minRR + (maxRR - minRR) * 0.75  // Medium-high quality
        else if qualityScore >= 2.0
            minRR + (maxRR - minRR) * 0.5   // Medium quality
        else if qualityScore >= 1.0
            minRR  // Low quality gets minimum R:R
        else
            math.max(1.0, minRR - 0.5)  // Very low quality
    else
        DynamicRR  // Original behavior when enhanced R:R is disabled

// Validate if entry meets quality requirements (for enhanced R:R system)
isValidEntry(qualityScore) =>
    valid = true

    // Quality score filter
    if qualityScore < minQualityScore
        valid := false

    valid

// Validate volume confirmation (works independently)
isVolumeValid() =>
    if requireVolumeConfirmation
        avgVol = ta.sma(volume, 20)
        volume >= avgVol * 1.1
    else
        true

//#region Intelligent Partial Profit Taking Functions
// Calculate intelligent partial TP levels based on setup quality and market structure
calculatePartialLevels(crt, entryPrice, slTarget, direction, qualityScore) =>
    baseDistance = math.abs(entryPrice - slTarget)

    if direction == "Long"
        // TP1: Always at 1:1 R:R for breakeven protection
        crt.partialTP1 := entryPrice + baseDistance * 1.0

        // TP2: Intelligent level based on setup quality
        tp2Multiplier = qualityScore >= 3.0 ? 1.8 : qualityScore >= 2.0 ? 1.6 : 1.4
        crt.partialTP2 := entryPrice + baseDistance * tp2Multiplier

        // Initialize trailing stop at original SL
        crt.trailingStop := slTarget
    else
        // TP1: Always at 1:1 R:R for breakeven protection
        crt.partialTP1 := entryPrice - baseDistance * 1.0

        // TP2: Intelligent level based on setup quality
        tp2Multiplier = qualityScore >= 3.0 ? 1.8 : qualityScore >= 2.0 ? 1.6 : 1.4
        crt.partialTP2 := entryPrice - baseDistance * tp2Multiplier

        // Initialize trailing stop at original SL
        crt.trailingStop := slTarget

// Update trailing stop logic after partial TPs are hit
updateTrailingStop(crt, currentHigh, currentLow) =>
    if useTrailingStop and crt.trailingActive
        if crt.entryType == "Long"
            // Trail stop below recent highs
            newTrailing = currentHigh - atrCRT * trailingATRMult
            // Only move trailing stop up, never down
            if newTrailing > crt.trailingStop
                crt.trailingStop := newTrailing
        else
            // Trail stop above recent lows
            newTrailing = currentLow + atrCRT * trailingATRMult
            // Only move trailing stop down, never up
            if newTrailing < crt.trailingStop
                crt.trailingStop := newTrailing

// Get current position status for display
getPositionStatus(crt) =>
    status = ""
    if crt.tp1Hit and crt.tp2Hit
        status := "Final " + str.tostring(finalTPPercent, "#") + "%"
    else if crt.tp1Hit
        status := str.tostring(100 - partialTP1Percent, "#") + "% Remaining"
    else
        status := "100% Active"
    status

// Calculate intelligent position sizing based on setup quality
getIntelligentSizing(qualityScore) =>
    // High quality setups: Let more run to final target
    // Low quality setups: Take more profits early
    if qualityScore >= 4.0
        [25.0, 35.0]  // Smaller partials, bigger final
    else if qualityScore >= 3.0
        [30.0, 40.0]  // Balanced approach
    else if qualityScore >= 2.0
        [35.0, 45.0]  // Larger partials, smaller final
    else
        [40.0, 50.0]  // Take profits early on low quality
//#endregion

// ============================================================================
// FVG AND ORDER BLOCK LOGIC (PRESERVED FROM ORIGINAL)
// ============================================================================

// FVG Type Definition
type FVGInfo
    int startTime
    float min
    float max
    bool isBull

type FVG
    FVGInfo info
    box fvgBox
    line topLine
    line bottomLine

// Order Block Type Definition
type orderBlockInfo
    int startTime
    float top
    float bottom
    string obType
    int breakTime = na

type orderBlock
    orderBlockInfo info
    box obBox

// CRT Type Definition with Strategy Enhancements
type CRT
    string state
    int startTime

    string overlapDirection
    int bulkyTimeLow
    int bulkyTimeHigh
    float bulkyHigh
    float bulkyLow
    int breakTime

    FVG fvg
    int fvgEndTime
    orderBlock ob

    float slTarget
    float tpTarget
    string entryType
    int entryTime
    int exitTime
    float entryPrice
    float exitPrice
    int dayEndedBeforeExit

    // Enhanced R:R fields
    float qualityScore = na
    float dynamicRR = na

    // Intelligent Partial Profit Taking fields
    float partialTP1 = na
    float partialTP2 = na
    bool tp1Hit = false
    bool tp2Hit = false
    float remainingPosition = 100.0
    float trailingStop = na
    bool trailingActive = false
    int tp1HitTime = na
    int tp2HitTime = na

// Global variables
var array<CRT> crtList = array.new<CRT>()
var CRT lastCRT = na
var array<FVG> allFVGList = array.new<FVG>()
var array<orderBlock> allOrderBlocksList = array.new<orderBlock>()

// Visual arrays
var array<line> lineX = array.new<line>()
var array<box> boxX = array.new<box>()
var array<label> labelX = array.new<label>()

// Settings
maxCRT = 5
maxDistanceToLastBar = 500

// Label size conversion
lblSize = switch lblSize
    "Tiny" => size.tiny
    "Small" => size.small
    "Normal" => size.normal
    "Large" => size.large
    "Huge" => size.huge
    => size.small

// ============================================================================
// STRATEGY ORDER MANAGEMENT FUNCTIONS
// ============================================================================

// Generate unique order IDs
getOrderID(prefix, timestamp) =>
    prefix + "_" + str.tostring(timestamp)

// Calculate position size for strategy orders
calculatePositionSize() =>
    baseSize = getPositionSize()
    // Ensure minimum position size
    math.max(baseSize, 1.0)

// Place strategy entry order
placeEntryOrder(direction, crt) =>
    if canOpenTrade() and strategy.position_size == 0
        posSize = calculatePositionSize()
        entryID = getOrderID(direction, crt.entryTime)

        // Create alert message if enabled
        alertMsg = useStrategyAlerts ?
          "CRT " + direction + " Entry | Q:" + str.tostring(crt.qualityScore, "#.#") +
          " | R:R:" + str.tostring(crt.dynamicRR, "#.##") : ""

        if direction == "Long"
            strategy.entry(entryID, strategy.long, qty=posSize, alert_message=alertMsg)
        else
            strategy.entry(entryID, strategy.short, qty=posSize, alert_message=alertMsg)

        // Update tracking variables
        currentEntryID := entryID
        inPosition := true
        positionDirection := direction
        entryPrice := close
        tp1Executed := false
        tp2Executed := false

        true
    else
        false

// Place partial TP orders
placePartialTPOrders(crt) =>
    if usePartialTPs and validPartialTPs and not na(currentEntryID)
        baseID = str.replace(currentEntryID, "Long_", "")
        baseID := str.replace(baseID, "Short_", "")

        // TP1 Order (30% of position)
        tp1ID = "TP1_" + baseID
        currentTP1ID := tp1ID

        // TP2 Order (40% of position)
        tp2ID = "TP2_" + baseID
        currentTP2ID := tp2ID

        // Final TP Order (remaining 30%)
        finalID = "Final_" + baseID
        currentFinalTPID := finalID

        // SL Order
        slID = "SL_" + baseID
        currentSLID := slID

        // Place orders with OCA group for proper management
        ocaGroup = "CRT_" + baseID

        if crt.entryType == "Long"
            // Partial TPs
            strategy.exit(tp1ID, currentEntryID, qty_percent=partialTP1Percent,
              limit=crt.partialTP1, oca_name=ocaGroup, oca_type=strategy.oca.reduce)
            strategy.exit(tp2ID, currentEntryID, qty_percent=partialTP2Percent,
              limit=crt.partialTP2, oca_name=ocaGroup, oca_type=strategy.oca.reduce)
            strategy.exit(finalID, currentEntryID,
              limit=crt.tpTarget, stop=crt.slTarget, oca_name=ocaGroup, oca_type=strategy.oca.reduce)
        else
            // Partial TPs for Short
            strategy.exit(tp1ID, currentEntryID, qty_percent=partialTP1Percent,
              limit=crt.partialTP1, oca_name=ocaGroup, oca_type=strategy.oca.reduce)
            strategy.exit(tp2ID, currentEntryID, qty_percent=partialTP2Percent,
              limit=crt.partialTP2, oca_name=ocaGroup, oca_type=strategy.oca.reduce)
            strategy.exit(finalID, currentEntryID,
              limit=crt.tpTarget, stop=crt.slTarget, oca_name=ocaGroup, oca_type=strategy.oca.reduce)
    else
        // Simple TP/SL when partial TPs disabled
        slID = "SL_" + str.replace(currentEntryID, "Long_", "")
        slID := str.replace(slID, "Short_", "")
        currentSLID := slID

        strategy.exit(slID, currentEntryID, limit=crt.tpTarget, stop=crt.slTarget)

// Update trailing stop
updateStrategyTrailingStop(crt) =>
    if useTrailingStop and crt.trailingActive and not na(currentFinalTPID)
        // Cancel existing final TP and replace with trailing stop
        strategy.cancel(currentFinalTPID)

        trailID = "Trail_" + str.replace(currentEntryID, "Long_", "")
        trailID := str.replace(trailID, "Short_", "")
        currentTrailID := trailID

        strategy.exit(trailID, currentEntryID, stop=crt.trailingStop, limit=crt.tpTarget)

// ============================================================================
// MAIN CRT LOGIC WITH STRATEGY INTEGRATION
// ============================================================================

// Position state synchronization
if strategy.position_size == 0 and inPosition
    // Position was closed, reset tracking
    inPosition := false
    positionDirection := na
    entryPrice := na
    currentEntryID := na
    currentTP1ID := na
    currentTP2ID := na
    currentFinalTPID := na
    currentSLID := na
    currentTrailID := na
    tp1Executed := false
    tp2Executed := false

// Track partial TP executions
if strategy.position_size > 0 and strategy.position_size < entryPrice and not tp1Executed
    tp1Executed := true
    if not na(lastCRT)
        lastCRT.tp1Hit := true
        lastCRT.tp1HitTime := time
        lastCRT.remainingPosition -= partialTP1Percent

        // Activate trailing stop after first partial
        if useTrailingStop
            lastCRT.trailingStop := lastCRT.entryPrice
            lastCRT.trailingActive := true

// FVG Detection Logic (Preserved from original)
var FVG latestFVG = na
if allFVGList.size() > 0
    latestFVG := allFVGList.get(0)

// Order Block Detection Logic (Preserved from original)
var orderBlock latestOB = na
if allOrderBlocksList.size() > 0
    latestOB := allOrderBlocksList.get(0)

// Main CRT State Machine
if bar_index > last_bar_index - maxDistanceToLastBar and barstate.isconfirmed
    if true
        createNewCRT = true
        if not na(lastCRT)
            if na(lastCRT.exitPrice) and lastCRT.state != "Aborted"
                createNewCRT := false // Don't enter if a trade is already entered

        if createNewCRT
            newCRT = CRT.new("Waiting For Bulky Candle", time)
            crtList.unshift(newCRT)
            lastCRT := newCRT

        if not na(lastCRT)
            // Waiting For Bulky Candle
            if lastCRT.state == "Waiting For Bulky Candle" and newBulkyCandle
                lastCRT.bulkyHigh := lastHigh
                lastCRT.bulkyLow := lastLow
                lastCRT.bulkyTimeLow := time // Simplified for strategy
                lastCRT.bulkyTimeHigh := time

                // Calculate setup quality score (only when enhanced R:R is enabled)
                if useEnhancedRR
                    bulkyRange = math.abs(lastHigh - lastLow)
                    lastCRT.qualityScore := setupQualityScore(bulkyRange, volume, atrCRT)
                    lastCRT.dynamicRR := getDynamicRR(lastCRT.qualityScore)
                else
                    lastCRT.qualityScore := na
                    lastCRT.dynamicRR := DynamicRR

                lastCRT.state := "Waiting For Side Retest"

            // Waiting For Side Retest
            else if lastCRT.state == "Waiting For Side Retest"
                if close > lastCRT.bulkyHigh
                    lastCRT.state := "Aborted"
                else if close < lastCRT.bulkyLow
                    lastCRT.state := "Aborted"

                if lastCRT.state != "Aborted"
                    bearOverlap = false
                    bullOverlap = false
                    if high > lastCRT.bulkyHigh and close <= lastCRT.bulkyHigh
                        bearOverlap := true
                    if low < lastCRT.bulkyLow and close >= lastCRT.bulkyLow
                        bullOverlap := true

                    if bearOverlap and not bullOverlap
                        lastCRT.overlapDirection := "Bear"
                        lastCRT.breakTime := time
                        lastCRT.state := "Enter Position" // Simplified for strategy

                    if bullOverlap and not bearOverlap
                        lastCRT.overlapDirection := "Bull"
                        lastCRT.breakTime := time
                        lastCRT.state := "Enter Position" // Simplified for strategy

    // Enter Position with Strategy Integration
    if not na(lastCRT)
        if lastCRT.state == "Enter Position"
            // Validate entry quality and volume confirmation
            qualityValid = useEnhancedRR ? isValidEntry(lastCRT.qualityScore) : true
            volumeValid = isVolumeValid()
            entryValid = qualityValid and volumeValid

            if entryValid
                lastCRT.state := "Entry Taken"
                lastCRT.entryTime := time
                lastCRT.entryPrice := close

                if lastCRT.overlapDirection == "Bull"
                    lastCRT.entryType := "Long"
                    if tpslMethod == "Fixed"
                        lastCRT.slTarget := lastCRT.entryPrice * (1 - slPercent / 100.0)
                        lastCRT.tpTarget := lastCRT.entryPrice * (1 + tpPercent / 100.0)
                    else if tpslMethod == "Dynamic"
                        lastCRT.slTarget := lastCRT.entryPrice - atrCRT * slATRMult
                        enhancedRR = useEnhancedRR ? lastCRT.dynamicRR : DynamicRR
                        lastCRT.tpTarget := lastCRT.entryPrice + (math.abs(lastCRT.entryPrice - lastCRT.slTarget) * enhancedRR)
                else
                    lastCRT.entryType := "Short"
                    if tpslMethod == "Fixed"
                        lastCRT.slTarget := lastCRT.entryPrice * (1 + slPercent / 100.0)
                        lastCRT.tpTarget := lastCRT.entryPrice * (1 - tpPercent / 100.0)
                    else if tpslMethod == "Dynamic"
                        lastCRT.slTarget := lastCRT.entryPrice + atrCRT * slATRMult
                        enhancedRR = useEnhancedRR ? lastCRT.dynamicRR : DynamicRR
                        lastCRT.tpTarget := lastCRT.entryPrice - (math.abs(lastCRT.entryPrice - lastCRT.slTarget) * enhancedRR)

                // Calculate partial TP levels when partial TP system is enabled and valid
                if usePartialTPs and validPartialTPs
                    qualityForPartials = useEnhancedRR ? lastCRT.qualityScore : 2.5
                    calculatePartialLevels(lastCRT, lastCRT.entryPrice, lastCRT.slTarget, lastCRT.entryType, qualityForPartials)
                else if usePartialTPs and not validPartialTPs
                    log.info("Warning: Partial TP percentages invalid (total > 90%). Using original TP/SL method.")

                // Place strategy orders
                if placeEntryOrder(lastCRT.entryType, lastCRT)
                    placePartialTPOrders(lastCRT)
            else
                // Provide specific rejection reason
                rejectionReason = ""
                if not qualityValid
                    rejectionReason += "Quality Score: " + str.tostring(lastCRT.qualityScore, "#.#") + " below minimum: " + str.tostring(minQualityScore, "#.#")
                if not volumeValid
                    if rejectionReason != ""
                        rejectionReason += " | "
                    rejectionReason += "Volume too low"
                log.info("Entry Rejected - " + rejectionReason)
                lastCRT.state := "Aborted"

    // Update trailing stops for active positions
    if not na(lastCRT) and lastCRT.state == "Entry Taken" and strategy.position_size != 0
        if usePartialTPs and validPartialTPs and lastCRT.trailingActive
            updateTrailingStop(lastCRT, high, low)
            updateStrategyTrailingStop(lastCRT)

// ============================================================================
// STRATEGY BACKTESTING DASHBOARD
// ============================================================================

if barstate.islast and backtestDisplayEnabled
    // Position for dashboard
    getPosition(location) =>
        switch location
            "Top Left" => position.top_left
            "Top Center" => position.top_center
            "Top Right" => position.top_right
            "Middle Left" => position.middle_left
            "Middle Center" => position.middle_center
            "Middle Right" => position.middle_right
            "Bottom Left" => position.bottom_left
            "Bottom Center" => position.bottom_center
            "Bottom Right" => position.bottom_right
            => position.top_right

    var table strategyDisplay = table.new(getPosition(backtestingLocation), 2, 15,
      bgcolor = screenerColor,
      frame_width = 2,
      frame_color = color.black,
      border_width = 1,
      border_color = color.black)

    // Strategy Performance Metrics
    totalTrades = strategy.closedtrades
    winningTrades = strategy.wintrades
    losingTrades = strategy.losstrades
    winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0

    totalProfit = strategy.netprofit
    totalProfitPercent = strategy.equity > 0 ? (totalProfit / (strategy.equity - totalProfit)) * 100 : 0
    avgWin = strategy.wintrades > 0 ? strategy.grossprofit / strategy.wintrades : 0
    avgLoss = strategy.losstrades > 0 ? strategy.grossloss / strategy.losstrades : 0
    profitFactor = strategy.grossloss != 0 ? strategy.grossprofit / math.abs(strategy.grossloss) : 0

    maxDrawdown = strategy.max_drawdown
    maxDrawdownPercent = strategy.equity > 0 ? (maxDrawdown / strategy.equity) * 100 : 0

    // Header
    table.merge_cells(strategyDisplay, 0, 0, 1, 0)
    table.cell(strategyDisplay, 0, 0, "CRT Strategy Performance", text_color = color.white, bgcolor = screenerColor)

    // Total Trades
    table.cell(strategyDisplay, 0, 1, "Total Trades", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 1, str.tostring(totalTrades), text_color = color.white, bgcolor = screenerColor)

    // Wins
    table.cell(strategyDisplay, 0, 2, "Wins", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 2, str.tostring(winningTrades), text_color = color.white, bgcolor = screenerColor)

    // Losses
    table.cell(strategyDisplay, 0, 3, "Losses", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 3, str.tostring(losingTrades), text_color = color.white, bgcolor = screenerColor)

    // Win Rate
    table.cell(strategyDisplay, 0, 4, "Win Rate", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 4, str.tostring(winRate, "#.##") + "%", text_color = color.white, bgcolor = screenerColor)

    // Net Profit
    table.cell(strategyDisplay, 0, 5, "Net Profit", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 5, "$" + str.tostring(totalProfit, "#.##"), text_color = color.white, bgcolor = screenerColor)

    // Net Profit %
    table.cell(strategyDisplay, 0, 6, "Net Profit %", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 6, str.tostring(totalProfitPercent, "#.##") + "%", text_color = color.white, bgcolor = screenerColor)

    // Profit Factor
    table.cell(strategyDisplay, 0, 7, "Profit Factor", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 7, str.tostring(profitFactor, "#.##"), text_color = color.white, bgcolor = screenerColor)

    // Max Drawdown
    table.cell(strategyDisplay, 0, 8, "Max Drawdown", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 8, str.tostring(maxDrawdownPercent, "#.##") + "%", text_color = color.white, bgcolor = screenerColor)

    // Enhanced R:R Status
    table.cell(strategyDisplay, 0, 9, "Enhanced R:R", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 9, useEnhancedRR ? "Enabled" : "Disabled", text_color = color.white, bgcolor = screenerColor)

    // R:R Range (when enhanced is enabled)
    if useEnhancedRR
        table.cell(strategyDisplay, 0, 10, "R:R Range", text_color = color.white, bgcolor = screenerColor)
        table.cell(strategyDisplay, 1, 10, str.tostring(minRR, "#.#") + " - " + str.tostring(maxRR, "#.#"), text_color = color.white, bgcolor = screenerColor)

    // Partial TP Status
    table.cell(strategyDisplay, 0, 11, "Partial TPs", text_color = color.white, bgcolor = screenerColor)
    partialStatus = usePartialTPs ? (validPartialTPs ? "Enabled" : "Invalid %") : "Disabled"
    table.cell(strategyDisplay, 1, 11, partialStatus, text_color = color.white, bgcolor = screenerColor)

    // Partial TP Configuration (when enabled)
    if usePartialTPs
        table.cell(strategyDisplay, 0, 12, "TP Sizing", text_color = color.white, bgcolor = screenerColor)
        table.cell(strategyDisplay, 1, 12, str.tostring(partialTP1Percent, "#") + "% / " + str.tostring(partialTP2Percent, "#") + "% / " + str.tostring(finalTPPercent, "#") + "%",
          text_color = color.white, bgcolor = screenerColor)

    // Position Size Info
    table.cell(strategyDisplay, 0, 13, "Position Size", text_color = color.white, bgcolor = screenerColor)
    table.cell(strategyDisplay, 1, 13, positionSizeType + ": " + str.tostring(positionSizeValue, "#.##"),
      text_color = color.white, bgcolor = screenerColor)

    // Current Position
    table.cell(strategyDisplay, 0, 14, "Current Position", text_color = color.white, bgcolor = screenerColor)
    positionText = strategy.position_size == 0 ? "None" :
      (strategy.position_size > 0 ? "Long: " : "Short: ") + str.tostring(math.abs(strategy.position_size), "#.##")
    table.cell(strategyDisplay, 1, 14, positionText, text_color = color.white, bgcolor = screenerColor)
